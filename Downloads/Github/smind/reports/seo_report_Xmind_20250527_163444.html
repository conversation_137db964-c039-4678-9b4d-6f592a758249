
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO审计报告 - https://xmind.com</title>
    <link rel="stylesheet" href="static/style.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>SEO审计报告</h1>
            <div class="url">https://xmind.com</div>
            <div style="margin-top: 10px; opacity: 0.8;">
                生成时间: 2025-05-27 16:34:45
            </div>
        </div>



        <!-- 基础SEO -->
        
        <div class="section">
            <div class="section-header">
                <h2>🔍 基础SEO检查</h2>
            </div>
            <div class="section-content">
                <!-- 标题检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面标题</strong>
                        <div class="details">
                            内容: Xmind - Mind Mapping App<br>
                            长度: 24 字符
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>标题太短，建议至少30个字符</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                </div>
                

                <!-- Meta描述检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Meta描述</strong>
                        <div class="details">
                            内容: Xmind is the most professional and popular mind mapping tool. Millions of people use Xmind to clarify thinking, manage complex information, brainstorming, get work organized, remote and work from home WFH.<br>
                            长度: 205 字符
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>描述太长，建议不超过160个字符</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                </div>
                

                <!-- 标题结构检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>标题结构 (H1-H6)</strong>
                        <div class="details">
                            
                            H1: 1 个
                            
                            <br>内容: |with Xmind
                            
                            <br>
                            
                            H2: 6 个
                            
                            <br>内容: Capture, Connect, and Communicate Your Creative Ideas, Hear from Our Xmind Family, Whenever You Need to Be Focused, Outlined, and Presented...
                            
                            <br>
                            
                            H3: 14 个
                            
                            <br>内容: Outliner, Pitch Mode, ZEN Mode...
                            
                            <br>
                            
                            H4: 9 个
                            
                            <br>内容: Label and Note, Task and Marker, Web Link and Topic Link...
                            
                            <br>
                            
                            H5: 0 个
                            
                            <br>
                            
                            H6: 0 个
                            
                            <br>
                            
                        </div>
                        
                    </div>
                </div>
                

                <!-- 图片检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>图片优化</strong>
                        <div class="details">
                            总图片数: 107<br>
                            缺少alt属性: 1
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>1张图片缺少alt属性</li>
                                
                            </ul>
                        </div>
                        

                        <!-- 显示缺少ALT属性的图片详情 -->
                        
                        <div class="missing-alt-images">
                            <h4>缺少ALT属性的图片:</h4>
                            
                            <div class="missing-alt-item">
                                <div class="alt-status">
                                    <span class="alt-label">Alt</span>
                                    <span class="alt-missing">⚠ Missing</span>
                                </div>
                                <div class="image-info">
                                    <div class="image-url">
                                        <strong>URL:</strong>
                                        <a href="https://assets.xmind.net/www/assets/images/minibar/minibar-bg-grid-bbf248303d.png" target="_blank" class="image-link">https://assets.xmind.net/www/assets/images/minibar/minibar-bg-grid-bbf248303d.png</a>
                                    </div>
                                    <div class="image-preview">
                                        <img src="https://assets.xmind.net/www/assets/images/minibar/minibar-bg-grid-bbf248303d.png" alt="预览图" class="preview-img"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="preview-error" style="display:none;">
                                            <span>🖼️ 图片无法加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                        </div>
                        
                    </div>
                </div>
                

                <!-- OG标签检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Open Graph 标签</strong>
                        <div class="details">
                            完整性: 完整<br>
                            
                            标题: Xmind - Full-featured mind mapping and brainstorming tool.<br>
                            
                            
                            描述: Boost efficiency both in work and life. Millions of people love it.<br>
                            
                            
                            类型: website<br>
                            
                            
                            URL: <a href="https://xmind.app" target="_blank" class="file-link">
                                https://xmind.app
                            </a><br>
                            
                            
                            网站名: Xmind<br>
                            
                        </div>

                        <!-- OG图片预览 -->
                        
                        <div class="og-preview">
                            <h4>OG图片预览:</h4>
                            <div class="og-image-container">
                                <img src="https://assets.xmind.net/www/assets/images/social-tag/homepage2024-share-card-en-95b3e00cd3.png" alt="OG图片预览" class="og-preview-img"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                <div class="og-image-error" style="display:none;">
                                    <span>🖼️ 图片无法加载</span>
                                </div>
                                <div class="og-image-info">
                                    <strong>图片URL:</strong>
                                    <a href="https://assets.xmind.net/www/assets/images/social-tag/homepage2024-share-card-en-95b3e00cd3.png" target="_blank" class="image-link">
                                        https://assets.xmind.net/www/assets/images/social-tag/homepage2024-share-card-en-95b3e00cd3.png
                                    </a>
                                    
                                    <br><strong>尺寸:</strong> 3600 x 1800
                                    
                                </div>
                            </div>
                        </div>
                        

                        
                    </div>
                </div>
                

                <!-- Twitter Cards检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Twitter Cards</strong>
                        <div class="details">
                            完整性: 不完整<br>
                            
                            卡片类型: summary_large_image<br>
                            
                            
                            
                            
                            网站: @xmind<br>
                            
                        </div>

                        <!-- Twitter图片预览 -->
                        

                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>缺少 twitter:title 标签</li>
                                
                                <li>缺少 twitter:description 标签</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                </div>
                

                <!-- 网站图标检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>网站图标</strong>
                        <div class="details">
                            <!-- Favicon 预览 -->
                            <div class="icon-section">
                                <strong>Favicon:</strong> 存在
                                
                                <div class="icon-preview">
                                    
                                    <div class="icon-item">
                                        <div class="icon-display">
                                            <img src="https://xmind.com/favicon.ico"
                                                 alt="Favicon" class="favicon-img"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                            <span class="icon-error" style="display:none;">🖼️ 无法加载</span>
                                        </div>
                                        <div class="icon-url">
                                            <a href="https://xmind.com/favicon.ico" target="_blank" class="file-link icon-link">https://xmind.com/favicon.ico</a>
                                        </div>
                                    </div>
                                    
                                    <div class="icon-item">
                                        <div class="icon-display">
                                            <img src="https://xmind.com/webapp-icon/icon_48.png"
                                                 alt="Favicon" class="favicon-img"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                            <span class="icon-error" style="display:none;">🖼️ 无法加载</span>
                                        </div>
                                        <div class="icon-url">
                                            <a href="https://xmind.com/webapp-icon/icon_48.png" target="_blank" class="file-link icon-link">https://xmind.com/webapp-icon/icon_48.png</a>
                                        </div>
                                    </div>
                                    
                                    <div class="icon-item">
                                        <div class="icon-display">
                                            <img src="https://xmind.com/webapp-icon/icon_128.png"
                                                 alt="Favicon" class="favicon-img"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                            <span class="icon-error" style="display:none;">🖼️ 无法加载</span>
                                        </div>
                                        <div class="icon-url">
                                            <a href="https://xmind.com/webapp-icon/icon_128.png" target="_blank" class="file-link icon-link">https://xmind.com/webapp-icon/icon_128.png</a>
                                        </div>
                                    </div>
                                    
                                    <div class="icon-item">
                                        <div class="icon-display">
                                            <img src="https://xmind.com/webapp-icon/icon_57.png"
                                                 alt="Favicon" class="favicon-img"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                            <span class="icon-error" style="display:none;">🖼️ 无法加载</span>
                                        </div>
                                        <div class="icon-url">
                                            <a href="https://xmind.com/webapp-icon/icon_57.png" target="_blank" class="file-link icon-link">https://xmind.com/webapp-icon/icon_57.png</a>
                                        </div>
                                    </div>
                                    
                                    <div class="icon-item">
                                        <div class="icon-display">
                                            <img src="https://xmind.com/webapp-icon/icon_72.png"
                                                 alt="Favicon" class="favicon-img"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                            <span class="icon-error" style="display:none;">🖼️ 无法加载</span>
                                        </div>
                                        <div class="icon-url">
                                            <a href="https://xmind.com/webapp-icon/icon_72.png" target="_blank" class="file-link icon-link">https://xmind.com/webapp-icon/icon_72.png</a>
                                        </div>
                                    </div>
                                    
                                    <div class="icon-item">
                                        <div class="icon-display">
                                            <img src="https://xmind.com/webapp-icon/icon_114.png"
                                                 alt="Favicon" class="favicon-img"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                            <span class="icon-error" style="display:none;">🖼️ 无法加载</span>
                                        </div>
                                        <div class="icon-url">
                                            <a href="https://xmind.com/webapp-icon/icon_114.png" target="_blank" class="file-link icon-link">https://xmind.com/webapp-icon/icon_114.png</a>
                                        </div>
                                    </div>
                                    
                                    <div class="icon-item">
                                        <div class="icon-display">
                                            <img src="https://xmind.com/webapp-icon/icon_144.png"
                                                 alt="Favicon" class="favicon-img"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                            <span class="icon-error" style="display:none;">🖼️ 无法加载</span>
                                        </div>
                                        <div class="icon-url">
                                            <a href="https://xmind.com/webapp-icon/icon_144.png" target="_blank" class="file-link icon-link">https://xmind.com/webapp-icon/icon_144.png</a>
                                        </div>
                                    </div>
                                    
                                    <div class="icon-item">
                                        <div class="icon-display">
                                            <img src="https://xmind.com/webapp-icon/icon_152.png"
                                                 alt="Favicon" class="favicon-img"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                            <span class="icon-error" style="display:none;">🖼️ 无法加载</span>
                                        </div>
                                        <div class="icon-url">
                                            <a href="https://xmind.com/webapp-icon/icon_152.png" target="_blank" class="file-link icon-link">https://xmind.com/webapp-icon/icon_152.png</a>
                                        </div>
                                    </div>
                                    
                                    <div class="icon-item">
                                        <div class="icon-display">
                                            <img src="https://xmind.com/webapp-icon/icon_192.png"
                                                 alt="Favicon" class="favicon-img"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                            <span class="icon-error" style="display:none;">🖼️ 无法加载</span>
                                        </div>
                                        <div class="icon-url">
                                            <a href="https://xmind.com/webapp-icon/icon_192.png" target="_blank" class="file-link icon-link">https://xmind.com/webapp-icon/icon_192.png</a>
                                        </div>
                                    </div>
                                    
                                    <div class="icon-item">
                                        <div class="icon-display">
                                            <img src="https://xmind.com/webapp-icon/icon_384.png"
                                                 alt="Favicon" class="favicon-img"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                            <span class="icon-error" style="display:none;">🖼️ 无法加载</span>
                                        </div>
                                        <div class="icon-url">
                                            <a href="https://xmind.com/webapp-icon/icon_384.png" target="_blank" class="file-link icon-link">https://xmind.com/webapp-icon/icon_384.png</a>
                                        </div>
                                    </div>
                                    
                                    <div class="icon-item">
                                        <div class="icon-display">
                                            <img src="https://xmind.com/webapp-icon/icon_512.png"
                                                 alt="Favicon" class="favicon-img"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                            <span class="icon-error" style="display:none;">🖼️ 无法加载</span>
                                        </div>
                                        <div class="icon-url">
                                            <a href="https://xmind.com/webapp-icon/icon_512.png" target="_blank" class="file-link icon-link">https://xmind.com/webapp-icon/icon_512.png</a>
                                        </div>
                                    </div>
                                    
                                    <div class="icon-item">
                                        <div class="icon-display">
                                            <img src="https://xmind.com/webapp-icon/icon_1024.png"
                                                 alt="Favicon" class="favicon-img"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                            <span class="icon-error" style="display:none;">🖼️ 无法加载</span>
                                        </div>
                                        <div class="icon-url">
                                            <a href="https://xmind.com/webapp-icon/icon_1024.png" target="_blank" class="file-link icon-link">https://xmind.com/webapp-icon/icon_1024.png</a>
                                        </div>
                                    </div>
                                    
                                </div>
                                
                            </div>

                            <!-- Apple Touch Icon 预览 -->
                            <div class="icon-section">
                                <strong>Apple Touch Icon:</strong> 存在
                                
                                <div class="icon-preview">
                                    
                                    <div class="icon-item">
                                        <div class="icon-display">
                                            <img src="https://xmind.com/webapp-icon/icon_128.png"
                                                 alt="Apple Touch Icon" class="apple-icon-img"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                            <span class="icon-error" style="display:none;">🖼️ 无法加载</span>
                                        </div>
                                        <div class="icon-url">
                                            <a href="https://xmind.com/webapp-icon/icon_128.png" target="_blank" class="file-link icon-link">https://xmind.com/webapp-icon/icon_128.png</a>
                                            
                                        </div>
                                    </div>
                                    
                                    <div class="icon-item">
                                        <div class="icon-display">
                                            <img src="https://xmind.com/webapp-icon/icon_57.png"
                                                 alt="Apple Touch Icon" class="apple-icon-img"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                            <span class="icon-error" style="display:none;">🖼️ 无法加载</span>
                                        </div>
                                        <div class="icon-url">
                                            <a href="https://xmind.com/webapp-icon/icon_57.png" target="_blank" class="file-link icon-link">https://xmind.com/webapp-icon/icon_57.png</a>
                                            
                                            <br><small>尺寸: 57x57</small>
                                            
                                        </div>
                                    </div>
                                    
                                    <div class="icon-item">
                                        <div class="icon-display">
                                            <img src="https://xmind.com/webapp-icon/icon_72.png"
                                                 alt="Apple Touch Icon" class="apple-icon-img"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                            <span class="icon-error" style="display:none;">🖼️ 无法加载</span>
                                        </div>
                                        <div class="icon-url">
                                            <a href="https://xmind.com/webapp-icon/icon_72.png" target="_blank" class="file-link icon-link">https://xmind.com/webapp-icon/icon_72.png</a>
                                            
                                            <br><small>尺寸: 72x72</small>
                                            
                                        </div>
                                    </div>
                                    
                                    <div class="icon-item">
                                        <div class="icon-display">
                                            <img src="https://xmind.com/webapp-icon/icon_114.png"
                                                 alt="Apple Touch Icon" class="apple-icon-img"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                            <span class="icon-error" style="display:none;">🖼️ 无法加载</span>
                                        </div>
                                        <div class="icon-url">
                                            <a href="https://xmind.com/webapp-icon/icon_114.png" target="_blank" class="file-link icon-link">https://xmind.com/webapp-icon/icon_114.png</a>
                                            
                                            <br><small>尺寸: 114x114</small>
                                            
                                        </div>
                                    </div>
                                    
                                    <div class="icon-item">
                                        <div class="icon-display">
                                            <img src="https://xmind.com/webapp-icon/icon_144.png"
                                                 alt="Apple Touch Icon" class="apple-icon-img"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                            <span class="icon-error" style="display:none;">🖼️ 无法加载</span>
                                        </div>
                                        <div class="icon-url">
                                            <a href="https://xmind.com/webapp-icon/icon_144.png" target="_blank" class="file-link icon-link">https://xmind.com/webapp-icon/icon_144.png</a>
                                            
                                            <br><small>尺寸: 144x144</small>
                                            
                                        </div>
                                    </div>
                                    
                                    <div class="icon-item">
                                        <div class="icon-display">
                                            <img src="https://xmind.com/webapp-icon/icon_152.png"
                                                 alt="Apple Touch Icon" class="apple-icon-img"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                            <span class="icon-error" style="display:none;">🖼️ 无法加载</span>
                                        </div>
                                        <div class="icon-url">
                                            <a href="https://xmind.com/webapp-icon/icon_152.png" target="_blank" class="file-link icon-link">https://xmind.com/webapp-icon/icon_152.png</a>
                                            
                                            <br><small>尺寸: 152x152</small>
                                            
                                        </div>
                                    </div>
                                    
                                    <div class="icon-item">
                                        <div class="icon-display">
                                            <img src="https://xmind.com/webapp-icon/icon_192.png"
                                                 alt="Apple Touch Icon" class="apple-icon-img"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                            <span class="icon-error" style="display:none;">🖼️ 无法加载</span>
                                        </div>
                                        <div class="icon-url">
                                            <a href="https://xmind.com/webapp-icon/icon_192.png" target="_blank" class="file-link icon-link">https://xmind.com/webapp-icon/icon_192.png</a>
                                            
                                            <br><small>尺寸: 192x192</small>
                                            
                                        </div>
                                    </div>
                                    
                                    <div class="icon-item">
                                        <div class="icon-display">
                                            <img src="https://xmind.com/webapp-icon/icon_384.png"
                                                 alt="Apple Touch Icon" class="apple-icon-img"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                            <span class="icon-error" style="display:none;">🖼️ 无法加载</span>
                                        </div>
                                        <div class="icon-url">
                                            <a href="https://xmind.com/webapp-icon/icon_384.png" target="_blank" class="file-link icon-link">https://xmind.com/webapp-icon/icon_384.png</a>
                                            
                                            <br><small>尺寸: 384x384</small>
                                            
                                        </div>
                                    </div>
                                    
                                    <div class="icon-item">
                                        <div class="icon-display">
                                            <img src="https://xmind.com/webapp-icon/icon_512.png"
                                                 alt="Apple Touch Icon" class="apple-icon-img"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                            <span class="icon-error" style="display:none;">🖼️ 无法加载</span>
                                        </div>
                                        <div class="icon-url">
                                            <a href="https://xmind.com/webapp-icon/icon_512.png" target="_blank" class="file-link icon-link">https://xmind.com/webapp-icon/icon_512.png</a>
                                            
                                            <br><small>尺寸: 512x512</small>
                                            
                                        </div>
                                    </div>
                                    
                                    <div class="icon-item">
                                        <div class="icon-display">
                                            <img src="https://xmind.com/webapp-icon/icon_1024.png"
                                                 alt="Apple Touch Icon" class="apple-icon-img"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                            <span class="icon-error" style="display:none;">🖼️ 无法加载</span>
                                        </div>
                                        <div class="icon-url">
                                            <a href="https://xmind.com/webapp-icon/icon_1024.png" target="_blank" class="file-link icon-link">https://xmind.com/webapp-icon/icon_1024.png</a>
                                            
                                            <br><small>尺寸: 1024x1024</small>
                                            
                                        </div>
                                    </div>
                                    
                                </div>
                                
                            </div>
                        </div>
                        
                    </div>
                </div>
                

                <!-- Canonical URL检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Canonical URL</strong>
                        <div class="details">
                            存在: 是<br>
                            
                            URL: <a href="https://xmind.app" target="_blank" class="file-link">
                                https://xmind.app
                            </a>
                            
                        </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 技术SEO -->
        
        <div class="section">
            <div class="section-header">
                <h2>⚙️ 技术SEO检查</h2>
            </div>
            <div class="section-content">
                <!-- Robots.txt检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Robots.txt</strong>
                        <div class="details">
                            存在: 是<br>
                            
                            URL: <a href="https://xmind.com/robots.txt" target="_blank" class="file-link">
                                https://xmind.com/robots.txt
                            </a>
                            
                            <span class="link-status status-good">✅ 可访问</span>
                            
                            
                        </div>
                        
                    </div>
                </div>
                

                <!-- Sitemap检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>XML网站地图</strong>
                        <div class="details">
                            存在: 是<br>
                            
                            URL: <a href="https://xmind.com/sitemap.xml" target="_blank" class="file-link">
                                https://xmind.com/sitemap.xml
                            </a>
                            <span class="link-status status-good">✅ 可访问</span>
                            
                            

                            
                        </div>
                        
                    </div>
                </div>
                

                <!-- 内部链接检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>内部链接</strong>
                        <div class="details">
                            数量: 93
                        </div>
                        
                    </div>
                </div>
                

                <!-- 缓存策略检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>缓存策略</strong>
                        <div class="details">
                            配置状态: 已配置<br>
                            
                            Cache-Control: no-cache<br>
                            
                            
                            ETag: "1d010-/zqjMm8gdC167Bzd+UYKQMMkEoU"<br>
                            
                            
                            
                        </div>
                        
                    </div>
                </div>
                

                <!-- PWA检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>PWA支持</strong>
                        <div class="details">
                            就绪状态: 未就绪<br>
                            Manifest: 缺失<br>
                            
                            Service Worker: 未检测到<br>
                            Viewport: 存在<br>
                            Theme Color: 存在
                            
                            ()
                            
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>缺少manifest.json</li>
                                
                                <li>未检测到Service Worker注册</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 性能检查 -->
        
        <div class="section">
            <div class="section-header">
                <h2>🚀 性能检查</h2>
            </div>
            <div class="section-content">
                <!-- 加载时间检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面加载时间</strong>
                        <div class="details">
                            时间: 0.33 秒
                        </div>
                        
                    </div>
                </div>
                

                <!-- 页面大小检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面大小</strong>
                        <div class="details">
                            大小: 116.02 KB
                        </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 移动端友好性 -->
        
        <div class="section">
            <div class="section-header">
                <h2>📱 移动端友好性</h2>
            </div>
            <div class="section-content">
                <!-- Viewport检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Viewport设置</strong>
                        <div class="details">
                            存在: 是
                            
                            <br>内容: width=device-width,initial-scale=1,viewport-fit=cover
                            
                        </div>
                        
                    </div>
                </div>
                

                <!-- 响应式设计检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>响应式设计</strong>
                        <div class="details">
                            检测到: 否
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>建议添加响应式设计支持</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 安全检查 -->
        
        <div class="section">
            <div class="section-header">
                <h2>🔒 安全检查</h2>
            </div>
            <div class="section-content">
                <!-- HTTPS检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>HTTPS加密</strong>
                        <div class="details">
                            启用: 是
                        </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 内容分析 -->
        
        <div class="section">
            <div class="section-header">
                <h2>📝 内容分析</h2>
            </div>
            <div class="section-content">
                <!-- 内容长度检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>内容长度</strong>
                        <div class="details">
                            词数: 561
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>建议增加内容到1000个词以上</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 页脚 -->
        <div class="footer">
            <p>报告由 测试工程师团队 SEO审计工具生成</p>
            <p>生成时间: 2025-05-27 16:34:45</p>
        </div>
    </div>
</body>
</html>
        