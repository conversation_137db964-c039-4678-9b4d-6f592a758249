
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO审计报告 - https://github.com</title>
    <link rel="stylesheet" href="static/style.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>SEO审计报告</h1>
            <div class="url">https://github.com</div>
            <div style="margin-top: 10px; opacity: 0.8;">
                生成时间: 2025-05-27 16:25:44
            </div>
        </div>



        <!-- 基础SEO -->
        
        <div class="section">
            <div class="section-header">
                <h2>🔍 基础SEO检查</h2>
            </div>
            <div class="section-content">
                <!-- 标题检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面标题</strong>
                        <div class="details">
                            内容: GitHub · Build and ship software on a single, collaborative platform · GitHub<br>
                            长度: 77 字符
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>标题太长，建议不超过60个字符</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                </div>
                

                <!-- Meta描述检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Meta描述</strong>
                        <div class="details">
                            内容: GitHub is where people build software. More than 150 million people use GitHub to discover, fork, and contribute to over 420 million projects.<br>
                            长度: 142 字符
                        </div>
                        
                    </div>
                </div>
                

                <!-- 标题结构检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>标题结构 (H1-H6)</strong>
                        <div class="details">
                            
                            H1: 4 个
                            
                            <br>内容: Search code, repositories, users, issues, pull requests..., Provide feedback, Saved searches...
                            
                            <br>
                            
                            H2: 11 个
                            
                            <br>内容: Navigation Menu, Use saved searches to filter your results more quickly, GitHub features...
                            
                            <br>
                            
                            H3: 16 个
                            
                            <br>内容: Work 55% faster.Jump to footnote 1 Increase productivity with AI-powered coding assistance, including code completion, chat, and more., Automate any workflow, Get up and running in seconds...
                            
                            <br>
                            
                            H4: 0 个
                            
                            <br>
                            
                            H5: 0 个
                            
                            <br>
                            
                            H6: 0 个
                            
                            <br>
                            
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>每个页面应该只有一个H1标签</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                </div>
                

                <!-- 图片检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>图片优化</strong>
                        <div class="details">
                            总图片数: 54<br>
                            缺少alt属性: 14
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>14张图片缺少alt属性</li>
                                
                            </ul>
                        </div>
                        

                        <!-- 显示缺少ALT属性的图片详情 -->
                        
                        <div class="missing-alt-images">
                            <h4>缺少ALT属性的图片:</h4>
                            
                            <div class="missing-alt-item">
                                <div class="alt-status">
                                    <span class="alt-label">Alt</span>
                                    <span class="alt-missing">⚠ Missing</span>
                                </div>
                                <div class="image-info">
                                    <div class="image-url">
                                        <strong>URL:</strong>
                                        <a href="https://github.githubassets.com/assets/particles-de1dd20f3008.png" target="_blank" class="image-link">https://github.githubassets.com/assets/particles-de1dd20f3008.png</a>
                                    </div>
                                    <div class="image-preview">
                                        <img src="https://github.githubassets.com/assets/particles-de1dd20f3008.png" alt="预览图" class="preview-img"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="preview-error" style="display:none;">
                                            <span>🖼️ 图片无法加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="missing-alt-item">
                                <div class="alt-status">
                                    <span class="alt-label">Alt</span>
                                    <span class="alt-missing">⚠ Missing</span>
                                </div>
                                <div class="image-info">
                                    <div class="image-url">
                                        <strong>URL:</strong>
                                        <a href="https://github.githubassets.com/assets/accordion-1-ce487d44c0bf.webp" target="_blank" class="image-link">https://github.githubassets.com/assets/accordion-1-ce487d44c0bf.webp</a>
                                    </div>
                                    <div class="image-preview">
                                        <img src="https://github.githubassets.com/assets/accordion-1-ce487d44c0bf.webp" alt="预览图" class="preview-img"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="preview-error" style="display:none;">
                                            <span>🖼️ 图片无法加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="missing-alt-item">
                                <div class="alt-status">
                                    <span class="alt-label">Alt</span>
                                    <span class="alt-missing">⚠ Missing</span>
                                </div>
                                <div class="image-info">
                                    <div class="image-url">
                                        <strong>URL:</strong>
                                        <a href="https://github.githubassets.com/assets/accordion-2-730955545f07.webp" target="_blank" class="image-link">https://github.githubassets.com/assets/accordion-2-730955545f07.webp</a>
                                    </div>
                                    <div class="image-preview">
                                        <img src="https://github.githubassets.com/assets/accordion-2-730955545f07.webp" alt="预览图" class="preview-img"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="preview-error" style="display:none;">
                                            <span>🖼️ 图片无法加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="missing-alt-item">
                                <div class="alt-status">
                                    <span class="alt-label">Alt</span>
                                    <span class="alt-missing">⚠ Missing</span>
                                </div>
                                <div class="image-info">
                                    <div class="image-url">
                                        <strong>URL:</strong>
                                        <a href="https://github.githubassets.com/assets/accordion-3-52ca331d22ea.webp" target="_blank" class="image-link">https://github.githubassets.com/assets/accordion-3-52ca331d22ea.webp</a>
                                    </div>
                                    <div class="image-preview">
                                        <img src="https://github.githubassets.com/assets/accordion-3-52ca331d22ea.webp" alt="预览图" class="preview-img"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="preview-error" style="display:none;">
                                            <span>🖼️ 图片无法加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="missing-alt-item">
                                <div class="alt-status">
                                    <span class="alt-label">Alt</span>
                                    <span class="alt-missing">⚠ Missing</span>
                                </div>
                                <div class="image-info">
                                    <div class="image-url">
                                        <strong>URL:</strong>
                                        <a href="https://github.githubassets.com/assets/accordion-4-a26744b70ff7.webp" target="_blank" class="image-link">https://github.githubassets.com/assets/accordion-4-a26744b70ff7.webp</a>
                                    </div>
                                    <div class="image-preview">
                                        <img src="https://github.githubassets.com/assets/accordion-4-a26744b70ff7.webp" alt="预览图" class="preview-img"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="preview-error" style="display:none;">
                                            <span>🖼️ 图片无法加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="missing-alt-item">
                                <div class="alt-status">
                                    <span class="alt-label">Alt</span>
                                    <span class="alt-missing">⚠ Missing</span>
                                </div>
                                <div class="image-info">
                                    <div class="image-url">
                                        <strong>URL:</strong>
                                        <a href="https://github.githubassets.com/assets/particles-de1dd20f3008.png" target="_blank" class="image-link">https://github.githubassets.com/assets/particles-de1dd20f3008.png</a>
                                    </div>
                                    <div class="image-preview">
                                        <img src="https://github.githubassets.com/assets/particles-de1dd20f3008.png" alt="预览图" class="preview-img"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="preview-error" style="display:none;">
                                            <span>🖼️ 图片无法加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="missing-alt-item">
                                <div class="alt-status">
                                    <span class="alt-label">Alt</span>
                                    <span class="alt-missing">⚠ Missing</span>
                                </div>
                                <div class="image-info">
                                    <div class="image-url">
                                        <strong>URL:</strong>
                                        <a href="https://github.githubassets.com/assets/particles-de1dd20f3008.png" target="_blank" class="image-link">https://github.githubassets.com/assets/particles-de1dd20f3008.png</a>
                                    </div>
                                    <div class="image-preview">
                                        <img src="https://github.githubassets.com/assets/particles-de1dd20f3008.png" alt="预览图" class="preview-img"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="preview-error" style="display:none;">
                                            <span>🖼️ 图片无法加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="missing-alt-item">
                                <div class="alt-status">
                                    <span class="alt-label">Alt</span>
                                    <span class="alt-missing">⚠ Missing</span>
                                </div>
                                <div class="image-info">
                                    <div class="image-url">
                                        <strong>URL:</strong>
                                        <a href="https://github.githubassets.com/assets/accordion-1-38ad6b6d1b20.webp" target="_blank" class="image-link">https://github.githubassets.com/assets/accordion-1-38ad6b6d1b20.webp</a>
                                    </div>
                                    <div class="image-preview">
                                        <img src="https://github.githubassets.com/assets/accordion-1-38ad6b6d1b20.webp" alt="预览图" class="preview-img"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="preview-error" style="display:none;">
                                            <span>🖼️ 图片无法加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="missing-alt-item">
                                <div class="alt-status">
                                    <span class="alt-label">Alt</span>
                                    <span class="alt-missing">⚠ Missing</span>
                                </div>
                                <div class="image-info">
                                    <div class="image-url">
                                        <strong>URL:</strong>
                                        <a href="https://github.githubassets.com/assets/accordion-2-c0a62cfc31a1.webp" target="_blank" class="image-link">https://github.githubassets.com/assets/accordion-2-c0a62cfc31a1.webp</a>
                                    </div>
                                    <div class="image-preview">
                                        <img src="https://github.githubassets.com/assets/accordion-2-c0a62cfc31a1.webp" alt="预览图" class="preview-img"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="preview-error" style="display:none;">
                                            <span>🖼️ 图片无法加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="missing-alt-item">
                                <div class="alt-status">
                                    <span class="alt-label">Alt</span>
                                    <span class="alt-missing">⚠ Missing</span>
                                </div>
                                <div class="image-info">
                                    <div class="image-url">
                                        <strong>URL:</strong>
                                        <a href="https://github.githubassets.com/assets/accordion-3-5d5d222f1830.webp" target="_blank" class="image-link">https://github.githubassets.com/assets/accordion-3-5d5d222f1830.webp</a>
                                    </div>
                                    <div class="image-preview">
                                        <img src="https://github.githubassets.com/assets/accordion-3-5d5d222f1830.webp" alt="预览图" class="preview-img"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="preview-error" style="display:none;">
                                            <span>🖼️ 图片无法加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="missing-alt-item">
                                <div class="alt-status">
                                    <span class="alt-label">Alt</span>
                                    <span class="alt-missing">⚠ Missing</span>
                                </div>
                                <div class="image-info">
                                    <div class="image-url">
                                        <strong>URL:</strong>
                                        <a href="https://github.githubassets.com/assets/accordion-4-7abff9233556.webp" target="_blank" class="image-link">https://github.githubassets.com/assets/accordion-4-7abff9233556.webp</a>
                                    </div>
                                    <div class="image-preview">
                                        <img src="https://github.githubassets.com/assets/accordion-4-7abff9233556.webp" alt="预览图" class="preview-img"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="preview-error" style="display:none;">
                                            <span>🖼️ 图片无法加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="missing-alt-item">
                                <div class="alt-status">
                                    <span class="alt-label">Alt</span>
                                    <span class="alt-missing">⚠ Missing</span>
                                </div>
                                <div class="image-info">
                                    <div class="image-url">
                                        <strong>URL:</strong>
                                        <a href="https://github.githubassets.com/assets/figma-62d390a52419.webp" target="_blank" class="image-link">https://github.githubassets.com/assets/figma-62d390a52419.webp</a>
                                    </div>
                                    <div class="image-preview">
                                        <img src="https://github.githubassets.com/assets/figma-62d390a52419.webp" alt="预览图" class="preview-img"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="preview-error" style="display:none;">
                                            <span>🖼️ 图片无法加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="missing-alt-item">
                                <div class="alt-status">
                                    <span class="alt-label">Alt</span>
                                    <span class="alt-missing">⚠ Missing</span>
                                </div>
                                <div class="image-info">
                                    <div class="image-url">
                                        <strong>URL:</strong>
                                        <a href="https://github.githubassets.com/assets/mercedes-benz-d8f89b041561.webp" target="_blank" class="image-link">https://github.githubassets.com/assets/mercedes-benz-d8f89b041561.webp</a>
                                    </div>
                                    <div class="image-preview">
                                        <img src="https://github.githubassets.com/assets/mercedes-benz-d8f89b041561.webp" alt="预览图" class="preview-img"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="preview-error" style="display:none;">
                                            <span>🖼️ 图片无法加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="missing-alt-item">
                                <div class="alt-status">
                                    <span class="alt-label">Alt</span>
                                    <span class="alt-missing">⚠ Missing</span>
                                </div>
                                <div class="image-info">
                                    <div class="image-url">
                                        <strong>URL:</strong>
                                        <a href="https://github.githubassets.com/assets/mercado-libre-579cb5447302.webp" target="_blank" class="image-link">https://github.githubassets.com/assets/mercado-libre-579cb5447302.webp</a>
                                    </div>
                                    <div class="image-preview">
                                        <img src="https://github.githubassets.com/assets/mercado-libre-579cb5447302.webp" alt="预览图" class="preview-img"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="preview-error" style="display:none;">
                                            <span>🖼️ 图片无法加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                        </div>
                        
                    </div>
                </div>
                

                <!-- OG标签检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Open Graph 标签</strong>
                        <div class="details">
                            完整性: 完整<br>
                            
                            标题: GitHub · Build and ship software on a single, collaborative platform<br>
                            
                            
                            描述: Join the world's most widely adopted, AI-powered developer platform where millions of developers, businesses, and the largest open source community build software that advances humanity.<br>
                            
                            
                            类型: object<br>
                            
                            
                            URL: <a href="https://github.com/" target="_blank" class="file-link">
                                https://github.com/
                            </a><br>
                            
                            
                            网站名: GitHub<br>
                            
                        </div>

                        <!-- OG图片预览 -->
                        
                        <div class="og-preview">
                            <h4>OG图片预览:</h4>
                            <div class="og-image-container">
                                <img src="https://github.githubassets.com/assets/home24-5939032587c9.jpg" alt="OG图片预览" class="og-preview-img"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                <div class="og-image-error" style="display:none;">
                                    <span>🖼️ 图片无法加载</span>
                                </div>
                                <div class="og-image-info">
                                    <strong>图片URL:</strong>
                                    <a href="https://github.githubassets.com/assets/home24-5939032587c9.jpg" target="_blank" class="image-link">
                                        https://github.githubassets.com/assets/home24-5939032587c9.jpg
                                    </a>
                                    
                                </div>
                            </div>
                        </div>
                        

                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>建议添加 og:image:width 和 og:image:height 标签</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                </div>
                

                <!-- Twitter Cards检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Twitter Cards</strong>
                        <div class="details">
                            完整性: 完整<br>
                            
                            卡片类型: summary_large_image<br>
                            
                            
                            标题: GitHub · Build and ship software on a single, collaborative platform<br>
                            
                            
                            描述: Join the world's most widely adopted, AI-powered developer platform where millions of developers, businesses, and the largest open source community build software that advances humanity.<br>
                            
                            
                            网站: @github<br>
                            
                        </div>

                        <!-- Twitter图片预览 -->
                        
                        <div class="twitter-preview">
                            <h4>Twitter图片预览:</h4>
                            <div class="twitter-image-container">
                                <img src="https://github.githubassets.com/assets/home24-5939032587c9.jpg" alt="Twitter图片预览" class="twitter-preview-img"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                <div class="twitter-image-error" style="display:none;">
                                    <span>🖼️ 图片无法加载</span>
                                </div>
                                <div class="twitter-image-info">
                                    <strong>图片URL:</strong>
                                    <a href="https://github.githubassets.com/assets/home24-5939032587c9.jpg" target="_blank" class="image-link">
                                        https://github.githubassets.com/assets/home24-5939032587c9.jpg
                                    </a>
                                </div>
                            </div>
                        </div>
                        

                        
                    </div>
                </div>
                

                <!-- 网站图标检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>网站图标</strong>
                        <div class="details">
                            Favicon: 存在<br>
                            Apple Touch Icon: 缺失<br>
                            
                            <strong>Favicon URLs:</strong><br>
                            
                            <a href="https://github.com/fluidicon.png" target="_blank" class="file-link icon-link">https://github.com/fluidicon.png</a>, 
                            
                            <a href="https://github.githubassets.com/assets/pinned-octocat-093da3e6fa40.svg" target="_blank" class="file-link icon-link">https://github.githubassets.com/assets/pinned-octocat-093da3e6fa40.svg</a>, 
                            
                            <a href="https://github.githubassets.com/favicons/favicon.png" target="_blank" class="file-link icon-link">https://github.githubassets.com/favicons/favicon.png</a>, 
                            
                            <a href="https://github.githubassets.com/favicons/favicon.svg" target="_blank" class="file-link icon-link">https://github.githubassets.com/favicons/favicon.svg</a>
                            <br>
                            
                            
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>缺少Apple Touch Icon</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                </div>
                

                <!-- Canonical URL检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Canonical URL</strong>
                        <div class="details">
                            存在: 是<br>
                            
                            URL: <a href="https://github.com" target="_blank" class="file-link">
                                https://github.com
                            </a>
                            
                        </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 技术SEO -->
        
        <div class="section">
            <div class="section-header">
                <h2>⚙️ 技术SEO检查</h2>
            </div>
            <div class="section-content">
                <!-- Robots.txt检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Robots.txt</strong>
                        <div class="details">
                            存在: 是<br>
                            
                            URL: <a href="https://github.com/robots.txt" target="_blank" class="file-link">
                                https://github.com/robots.txt
                            </a>
                            
                            <span class="link-status status-good">✅ 可访问</span>
                            
                            
                        </div>
                        
                    </div>
                </div>
                

                <!-- Sitemap检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>XML网站地图</strong>
                        <div class="details">
                            存在: 否<br>
                            

                            
                            <div class="checked-urls">
                                <strong>已检查的位置:</strong>
                                <ul class="url-list">
                                    
                                    <li>
                                        <a href="https://github.com/sitemap.xml" target="_blank" class="file-link checked-url">https://github.com/sitemap.xml</a>
                                        <span class="link-status status-warning">⚠️ 未找到</span>
                                    </li>
                                    
                                    <li>
                                        <a href="https://github.com/sitemap_index.xml" target="_blank" class="file-link checked-url">https://github.com/sitemap_index.xml</a>
                                        <span class="link-status status-warning">⚠️ 未找到</span>
                                    </li>
                                    
                                    <li>
                                        <a href="https://github.com/sitemaps.xml" target="_blank" class="file-link checked-url">https://github.com/sitemaps.xml</a>
                                        <span class="link-status status-warning">⚠️ 未找到</span>
                                    </li>
                                    
                                    <li>
                                        <a href="https://github.com/sitemap/sitemap.xml" target="_blank" class="file-link checked-url">https://github.com/sitemap/sitemap.xml</a>
                                        <span class="link-status status-warning">⚠️ 未找到</span>
                                    </li>
                                    
                                    <li>
                                        <a href="https://github.com/sitemap/index.xml" target="_blank" class="file-link checked-url">https://github.com/sitemap/index.xml</a>
                                        <span class="link-status status-warning">⚠️ 未找到</span>
                                    </li>
                                    
                                    <li>
                                        <a href="https://github.com/wp-sitemap.xml" target="_blank" class="file-link checked-url">https://github.com/wp-sitemap.xml</a>
                                        <span class="link-status status-warning">⚠️ 未找到</span>
                                    </li>
                                    
                                    <li>
                                        <a href="https://github.com/sitemap-index.xml" target="_blank" class="file-link checked-url">https://github.com/sitemap-index.xml</a>
                                        <span class="link-status status-warning">⚠️ 未找到</span>
                                    </li>
                                    
                                </ul>
                            </div>
                            
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>建议添加XML网站地图，常见位置: /sitemap.xml</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                </div>
                

                <!-- 内部链接检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>内部链接</strong>
                        <div class="details">
                            数量: 116
                        </div>
                        
                    </div>
                </div>
                

                <!-- 缓存策略检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>缓存策略</strong>
                        <div class="details">
                            配置状态: 已配置<br>
                            
                            Cache-Control: max-age=0, private, must-revalidate<br>
                            
                            
                            ETag: W/"714f92c36e13bea322336da02a1860ad"<br>
                            
                            
                            
                        </div>
                        
                    </div>
                </div>
                

                <!-- PWA检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>PWA支持</strong>
                        <div class="details">
                            就绪状态: 未就绪<br>
                            Manifest: 存在<br>
                            
                            Manifest URL: <a href="/manifest.json" target="_blank" class="file-link">
                                /manifest.json
                            </a><br>
                            
                            Service Worker: 未检测到<br>
                            Viewport: 存在<br>
                            Theme Color: 存在
                            
                            (#1e2327)
                            
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>未检测到Service Worker注册</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 性能检查 -->
        
        <div class="section">
            <div class="section-header">
                <h2>🚀 性能检查</h2>
            </div>
            <div class="section-content">
                <!-- 加载时间检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面加载时间</strong>
                        <div class="details">
                            时间: 0.4 秒
                        </div>
                        
                    </div>
                </div>
                

                <!-- 页面大小检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面大小</strong>
                        <div class="details">
                            大小: 281.85 KB
                        </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 移动端友好性 -->
        
        <div class="section">
            <div class="section-header">
                <h2>📱 移动端友好性</h2>
            </div>
            <div class="section-content">
                <!-- Viewport检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Viewport设置</strong>
                        <div class="details">
                            存在: 是
                            
                            <br>内容: width=device-width
                            
                        </div>
                        
                    </div>
                </div>
                

                <!-- 响应式设计检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>响应式设计</strong>
                        <div class="details">
                            检测到: 否
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>建议添加响应式设计支持</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 安全检查 -->
        
        <div class="section">
            <div class="section-header">
                <h2>🔒 安全检查</h2>
            </div>
            <div class="section-content">
                <!-- HTTPS检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>HTTPS加密</strong>
                        <div class="details">
                            启用: 是
                        </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 内容分析 -->
        
        <div class="section">
            <div class="section-header">
                <h2>📝 内容分析</h2>
            </div>
            <div class="section-content">
                <!-- 内容长度检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>内容长度</strong>
                        <div class="details">
                            词数: 1398
                        </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 页脚 -->
        <div class="footer">
            <p>报告由 测试工程师团队 SEO审计工具生成</p>
            <p>生成时间: 2025-05-27 16:25:44</p>
        </div>
    </div>
</body>
</html>
        