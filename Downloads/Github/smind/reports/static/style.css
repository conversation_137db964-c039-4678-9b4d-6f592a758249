
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header .url {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .score-overview {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .score-circle {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5em;
            font-weight: bold;
            color: white;
        }

        .score-a { background: #4CAF50; }
        .score-b { background: #8BC34A; }
        .score-c { background: #FFC107; }
        .score-d { background: #FF9800; }
        .score-f { background: #F44336; }

        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .section-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }

        .section-header h2 {
            color: #495057;
            font-size: 1.5em;
        }

        .section-content {
            padding: 20px;
        }

        .check-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .check-item:last-child {
            border-bottom: none;
        }

        .check-name {
            font-weight: 500;
            flex: 1;
        }

        .check-score {
            padding: 5px 15px;
            border-radius: 20px;
            color: white;
            font-weight: bold;
            margin-left: 10px;
        }

        .score-excellent { background: #4CAF50; }
        .score-good { background: #8BC34A; }
        .score-average { background: #FFC107; }
        .score-poor { background: #FF9800; }
        .score-bad { background: #F44336; }

        .recommendations {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
        }

        .recommendations h4 {
            color: #856404;
            margin-bottom: 10px;
        }

        .recommendations ul {
            list-style-type: none;
            padding-left: 0;
        }

        .recommendations li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }

        .recommendations li:before {
            content: "⚠";
            position: absolute;
            left: 0;
            color: #856404;
        }

        .details {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 0.9em;
        }

        .footer {
            text-align: center;
            padding: 30px;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
            margin-top: 30px;
        }

        /* 缺少ALT属性的图片样式 */
        .missing-alt-images {
            background: #fff8e1;
            border: 1px solid #ffcc02;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
        }

        .missing-alt-images h4 {
            color: #e65100;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .missing-alt-item {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 15px;
            overflow: hidden;
        }

        .missing-alt-item:last-child {
            margin-bottom: 0;
        }

        .alt-status {
            background: #f5f5f5;
            padding: 10px 15px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alt-label {
            font-weight: bold;
            color: #666;
            min-width: 30px;
        }

        .alt-missing {
            color: #ff9800;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .image-info {
            padding: 15px;
        }

        .image-url {
            margin-bottom: 15px;
            word-break: break-all;
        }

        .image-link {
            color: #1976d2;
            text-decoration: none;
            font-family: monospace;
            font-size: 0.9em;
        }

        .image-link:hover {
            text-decoration: underline;
        }

        .image-preview {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100px;
            background: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 10px;
        }

        .preview-img {
            max-width: 200px;
            max-height: 150px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .preview-error {
            color: #666;
            font-style: italic;
            text-align: center;
        }

        /* OG标签预览样式 */
        .og-preview {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
        }

        .og-preview h4 {
            color: #2e7d32;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .og-image-container {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }

        .og-preview-img {
            width: 100%;
            max-width: 400px;
            height: auto;
            display: block;
            margin: 0 auto;
        }

        .og-image-error {
            padding: 40px;
            text-align: center;
            color: #666;
            font-style: italic;
            background: #f9f9f9;
        }

        .og-image-info {
            padding: 15px;
            background: #f8f9fa;
            border-top: 1px solid #e0e0e0;
            word-break: break-all;
        }

        /* Twitter Cards预览样式 */
        .twitter-preview {
            background: #e3f2fd;
            border: 1px solid #1976d2;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
        }

        .twitter-preview h4 {
            color: #1565c0;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .twitter-image-container {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }

        .twitter-preview-img {
            width: 100%;
            max-width: 400px;
            height: auto;
            display: block;
            margin: 0 auto;
        }

        .twitter-image-error {
            padding: 40px;
            text-align: center;
            color: #666;
            font-style: italic;
            background: #f9f9f9;
        }

        .twitter-image-info {
            padding: 15px;
            background: #f8f9fa;
            border-top: 1px solid #e0e0e0;
            word-break: break-all;
        }

        /* 文件链接样式 */
        .file-link {
            color: #1976d2;
            text-decoration: none;
            font-weight: 500;
            border-bottom: 1px dotted #1976d2;
        }

        .file-link:hover {
            color: #0d47a1;
            text-decoration: none;
            border-bottom: 1px solid #0d47a1;
        }

        .link-status {
            margin-left: 10px;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.85em;
            font-weight: 500;
        }

        .status-good {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .status-warning {
            background: #fff3e0;
            color: #f57c00;
        }

        .checked-urls {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #ff9800;
        }

        .url-list {
            list-style: none;
            padding: 0;
            margin: 10px 0 0 0;
        }

        .url-list li {
            margin: 8px 0;
            padding: 8px;
            background: white;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
        }

        .checked-url {
            font-family: monospace;
            font-size: 0.9em;
        }

        .icon-link {
            font-size: 0.9em;
            margin: 2px 4px;
            display: inline-block;
        }

        /* 图标预览样式 */
        .icon-section {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }

        .icon-preview {
            margin-top: 10px;
        }

        .icon-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
        }

        .icon-display {
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 48px;
            height: 48px;
            background: #f5f5f5;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .favicon-img {
            max-width: 32px;
            max-height: 32px;
            width: auto;
            height: auto;
        }

        .apple-icon-img {
            max-width: 44px;
            max-height: 44px;
            width: auto;
            height: auto;
            border-radius: 8px;
        }

        .icon-error {
            font-size: 0.8em;
            color: #666;
        }

        .icon-url {
            flex: 1;
            word-break: break-all;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2em;
            }

            .score-circle {
                width: 120px;
                height: 120px;
                font-size: 2em;
            }

            .check-item {
                flex-direction: column;
                align-items: flex-start;
            }

            .check-score {
                margin-left: 0;
                margin-top: 10px;
            }
        }
        