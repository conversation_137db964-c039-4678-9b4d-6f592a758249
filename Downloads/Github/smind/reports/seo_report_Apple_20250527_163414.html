
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO审计报告 - https://apple.com</title>
    <link rel="stylesheet" href="static/style.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>SEO审计报告</h1>
            <div class="url">https://apple.com</div>
            <div style="margin-top: 10px; opacity: 0.8;">
                生成时间: 2025-05-27 16:34:14
            </div>
        </div>



        <!-- 基础SEO -->
        
        <div class="section">
            <div class="section-header">
                <h2>🔍 基础SEO检查</h2>
            </div>
            <div class="section-content">
                <!-- 标题检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面标题</strong>
                        <div class="details">
                            内容: Apple<br>
                            长度: 5 字符
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>标题太短，建议至少30个字符</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                </div>
                

                <!-- Meta描述检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Meta描述</strong>
                        <div class="details">
                            内容: 无<br>
                            长度: 0 字符
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>页面缺少meta描述</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                </div>
                

                <!-- 标题结构检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>标题结构 (H1-H6)</strong>
                        <div class="details">
                            
                            H1: 1 个
                            
                            <br>内容: Apple
                            
                            <br>
                            
                            H2: 4 个
                            
                            <br>内容: iPhone, Apple Watch Series 10, MacBook Air...
                            
                            <br>
                            
                            H3: 19 个
                            
                            <br>内容: WWDC 25, Apple Trade In, iPad Air...
                            
                            <br>
                            
                            H4: 0 个
                            
                            <br>
                            
                            H5: 0 个
                            
                            <br>
                            
                            H6: 0 个
                            
                            <br>
                            
                        </div>
                        
                    </div>
                </div>
                

                <!-- 图片检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>图片优化</strong>
                        <div class="details">
                            总图片数: 0<br>
                            缺少alt属性: 0
                        </div>
                        

                        <!-- 显示缺少ALT属性的图片详情 -->
                        
                    </div>
                </div>
                

                <!-- OG标签检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Open Graph 标签</strong>
                        <div class="details">
                            完整性: 完整<br>
                            
                            标题: Apple<br>
                            
                            
                            描述: Discover the innovative world of Apple and shop everything iPhone, iPad, Apple Watch, Mac, and Apple TV, plus explore accessories, entertainment, and expert device support.<br>
                            
                            
                            类型: website<br>
                            
                            
                            URL: <a href="https://www.apple.com/" target="_blank" class="file-link">
                                https://www.apple.com/
                            </a><br>
                            
                            
                            网站名: Apple<br>
                            
                        </div>

                        <!-- OG图片预览 -->
                        
                        <div class="og-preview">
                            <h4>OG图片预览:</h4>
                            <div class="og-image-container">
                                <img src="https://www.apple.com/ac/structured-data/images/open_graph_logo.png?202110180743" alt="OG图片预览" class="og-preview-img"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                <div class="og-image-error" style="display:none;">
                                    <span>🖼️ 图片无法加载</span>
                                </div>
                                <div class="og-image-info">
                                    <strong>图片URL:</strong>
                                    <a href="https://www.apple.com/ac/structured-data/images/open_graph_logo.png?202110180743" target="_blank" class="image-link">
                                        https://www.apple.com/ac/structured-data/images/open_graph_logo.png?202110180743
                                    </a>
                                    
                                </div>
                            </div>
                        </div>
                        

                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>建议添加 og:image:width 和 og:image:height 标签</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                </div>
                

                <!-- Twitter Cards检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Twitter Cards</strong>
                        <div class="details">
                            完整性: 不完整<br>
                            
                            
                            
                            
                        </div>

                        <!-- Twitter图片预览 -->
                        

                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>缺少 twitter:card 标签</li>
                                
                                <li>缺少 twitter:title 标签</li>
                                
                                <li>缺少 twitter:description 标签</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                </div>
                

                <!-- 网站图标检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>网站图标</strong>
                        <div class="details">
                            <!-- Favicon 预览 -->
                            <div class="icon-section">
                                <strong>Favicon:</strong> 缺失
                                
                            </div>

                            <!-- Apple Touch Icon 预览 -->
                            <div class="icon-section">
                                <strong>Apple Touch Icon:</strong> 缺失
                                
                            </div>
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>缺少favicon图标</li>
                                
                                <li>缺少Apple Touch Icon</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                </div>
                

                <!-- Canonical URL检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Canonical URL</strong>
                        <div class="details">
                            存在: 是<br>
                            
                            URL: <a href="https://www.apple.com/" target="_blank" class="file-link">
                                https://www.apple.com/
                            </a>
                            
                        </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 技术SEO -->
        
        <div class="section">
            <div class="section-header">
                <h2>⚙️ 技术SEO检查</h2>
            </div>
            <div class="section-content">
                <!-- Robots.txt检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Robots.txt</strong>
                        <div class="details">
                            存在: 是<br>
                            
                            URL: <a href="https://apple.com/robots.txt" target="_blank" class="file-link">
                                https://apple.com/robots.txt
                            </a>
                            
                            <span class="link-status status-good">✅ 可访问</span>
                            
                            
                        </div>
                        
                    </div>
                </div>
                

                <!-- Sitemap检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>XML网站地图</strong>
                        <div class="details">
                            存在: 是<br>
                            
                            URL: <a href="https://apple.com/sitemap.xml" target="_blank" class="file-link">
                                https://apple.com/sitemap.xml
                            </a>
                            <span class="link-status status-good">✅ 可访问</span>
                            
                            

                            
                        </div>
                        
                    </div>
                </div>
                

                <!-- 内部链接检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>内部链接</strong>
                        <div class="details">
                            数量: 118
                        </div>
                        
                    </div>
                </div>
                

                <!-- 缓存策略检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>缓存策略</strong>
                        <div class="details">
                            配置状态: 已配置<br>
                            
                            Cache-Control: no-store<br>
                            
                            
                            
                            
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>缺少ETag或Last-Modified头，无法进行条件请求</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                </div>
                

                <!-- PWA检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>PWA支持</strong>
                        <div class="details">
                            就绪状态: 未就绪<br>
                            Manifest: 缺失<br>
                            
                            Service Worker: 未检测到<br>
                            Viewport: 存在<br>
                            Theme Color: 缺失
                            
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>缺少manifest.json</li>
                                
                                <li>未检测到Service Worker注册</li>
                                
                                <li>建议添加theme-color meta标签</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 性能检查 -->
        
        <div class="section">
            <div class="section-header">
                <h2>🚀 性能检查</h2>
            </div>
            <div class="section-content">
                <!-- 加载时间检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面加载时间</strong>
                        <div class="details">
                            时间: 0.24 秒
                        </div>
                        
                    </div>
                </div>
                

                <!-- 页面大小检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面大小</strong>
                        <div class="details">
                            大小: 184.35 KB
                        </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 移动端友好性 -->
        
        <div class="section">
            <div class="section-header">
                <h2>📱 移动端友好性</h2>
            </div>
            <div class="section-content">
                <!-- Viewport检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Viewport设置</strong>
                        <div class="details">
                            存在: 是
                            
                            <br>内容: width=device-width, initial-scale=1, viewport-fit=cover
                            
                        </div>
                        
                    </div>
                </div>
                

                <!-- 响应式设计检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>响应式设计</strong>
                        <div class="details">
                            检测到: 否
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>建议添加响应式设计支持</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 安全检查 -->
        
        <div class="section">
            <div class="section-header">
                <h2>🔒 安全检查</h2>
            </div>
            <div class="section-content">
                <!-- HTTPS检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>HTTPS加密</strong>
                        <div class="details">
                            启用: 是
                        </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 内容分析 -->
        
        <div class="section">
            <div class="section-header">
                <h2>📝 内容分析</h2>
            </div>
            <div class="section-content">
                <!-- 内容长度检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>内容长度</strong>
                        <div class="details">
                            词数: 1046
                        </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 页脚 -->
        <div class="footer">
            <p>报告由 测试工程师团队 SEO审计工具生成</p>
            <p>生成时间: 2025-05-27 16:34:14</p>
        </div>
    </div>
</body>
</html>
        