import requests
import time
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import validators
import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import config

class SEOChecker:
    def __init__(self, url):
        self.url = url
        self.domain = urlparse(url).netloc
        self.results = {}
        self.soup = None
        self.response = None

    def check_all(self):
        """执行所有SEO检查"""
        print(f"开始检查网站: {self.url}")

        # 获取页面内容
        if not self._fetch_page():
            return None

        # 执行各项检查
        self._check_basic_seo()
        self._check_technical_seo()
        self._check_content_analysis()
        self._check_performance()
        self._check_mobile_friendly()
        self._check_security()

        return self.results

    def _fetch_page(self):
        """获取页面内容"""
        try:
            headers = {'User-Agent': config.USER_AGENT}
            self.response = requests.get(self.url, headers=headers, timeout=config.REQUEST_TIMEOUT)
            self.response.raise_for_status()

            self.soup = BeautifulSoup(self.response.content, 'html.parser')
            return True
        except Exception as e:
            print(f"获取页面失败: {e}")
            return False

    def _check_basic_seo(self):
        """基础SEO检查"""
        basic_seo = {}

        # 检查标题
        title_tag = self.soup.find('title')
        if title_tag:
            title = title_tag.get_text().strip()
            basic_seo['title'] = {
                'content': title,
                'length': len(title),
                'recommendations': self._get_title_recommendations(title)
            }
        else:
            basic_seo['title'] = {
                'content': '',
                'length': 0,
                'recommendations': ['页面缺少标题标签']
            }

        # 检查meta描述
        meta_desc = self.soup.find('meta', attrs={'name': 'description'})
        if meta_desc:
            description = meta_desc.get('content', '').strip()
            basic_seo['meta_description'] = {
                'content': description,
                'length': len(description),
                'recommendations': self._get_description_recommendations(description)
            }
        else:
            basic_seo['meta_description'] = {
                'content': '',
                'length': 0,
                'recommendations': ['页面缺少meta描述']
            }

        # 检查meta关键词
        meta_keywords = self.soup.find('meta', attrs={'name': 'keywords'})
        basic_seo['meta_keywords'] = {
            'content': meta_keywords.get('content', '') if meta_keywords else '',
            'recommendations': [] if meta_keywords else ['建议添加meta关键词']
        }

        # 检查标题结构
        basic_seo['headings'] = self._check_headings()

        # 检查图片
        basic_seo['images'] = self._check_images()

        # 检查OG信息
        basic_seo['og_tags'] = self._check_og_tags()

        # 检查Twitter Cards
        basic_seo['twitter_cards'] = self._check_twitter_cards()

        # 检查favicon和apple touch icon
        basic_seo['icons'] = self._check_icons()

        # 检查canonical URL
        basic_seo['canonical'] = self._check_canonical()

        self.results['basic_seo'] = basic_seo

    def _check_technical_seo(self):
        """技术SEO检查"""
        technical_seo = {}

        # 检查robots.txt
        technical_seo['robots_txt'] = self._check_robots_txt()

        # 检查sitemap
        technical_seo['sitemap'] = self._check_sitemap()

        # 检查URL结构
        technical_seo['url_structure'] = self._check_url_structure()

        # 检查内部链接
        technical_seo['internal_links'] = self._check_internal_links()

        # 检查外部链接
        technical_seo['external_links'] = self._check_external_links()

        # 检查缓存策略
        technical_seo['cache_policy'] = self._check_cache_policy()

        # 检查PWA相关
        technical_seo['pwa'] = self._check_pwa()

        self.results['technical_seo'] = technical_seo

    def _check_content_analysis(self):
        """内容分析"""
        content_analysis = {}

        # 获取页面文本内容
        text_content = self.soup.get_text()
        word_count = len(text_content.split())

        content_analysis['word_count'] = {
            'count': word_count,
            'recommendations': self._get_content_recommendations(word_count)
        }

        # 检查重复内容（简单检查）
        content_analysis['duplicate_content'] = self._check_duplicate_content()

        self.results['content_analysis'] = content_analysis

    def _check_performance(self):
        """性能检查"""
        performance = {}

        # 页面加载时间
        start_time = time.time()
        try:
            response = requests.get(self.url, timeout=config.REQUEST_TIMEOUT)
            load_time = time.time() - start_time
            performance['load_time'] = {
                'time': round(load_time, 2),
                'recommendations': self._get_performance_recommendations(load_time)
            }
        except Exception as e:
            performance['load_time'] = {
                'time': 0,
                'recommendations': [f'无法测试加载时间: {e}']
            }

        # 页面大小
        if self.response:
            page_size = len(self.response.content) / 1024  # KB
            performance['page_size'] = {
                'size': round(page_size, 2),
                'recommendations': self._get_size_recommendations(page_size)
            }

        self.results['performance'] = performance

    def _check_mobile_friendly(self):
        """移动端友好性检查"""
        mobile = {}

        # 检查viewport meta标签
        viewport = self.soup.find('meta', attrs={'name': 'viewport'})
        mobile['viewport'] = {
            'exists': bool(viewport),
            'content': viewport.get('content', '') if viewport else '',
            'recommendations': [] if viewport else ['添加viewport meta标签以支持移动设备']
        }

        # 检查响应式设计（简单检查CSS媒体查询）
        mobile['responsive'] = self._check_responsive_design()

        self.results['mobile'] = mobile

    def _check_security(self):
        """安全检查"""
        security = {}

        # 检查HTTPS
        is_https = self.url.startswith('https://')
        security['https'] = {
            'enabled': is_https,
            'recommendations': [] if is_https else ['建议使用HTTPS协议保护用户数据']
        }

        self.results['security'] = security

    # 辅助方法

    def _get_title_recommendations(self, title):
        """获取标题建议"""
        recommendations = []
        if not title:
            recommendations.append("添加页面标题")
        elif len(title) < config.MIN_TITLE_LENGTH:
            recommendations.append(f"标题太短，建议至少{config.MIN_TITLE_LENGTH}个字符")
        elif len(title) > config.MAX_TITLE_LENGTH:
            recommendations.append(f"标题太长，建议不超过{config.MAX_TITLE_LENGTH}个字符")
        return recommendations



    def _get_description_recommendations(self, description):
        """获取描述建议"""
        recommendations = []
        if not description:
            recommendations.append("添加meta描述")
        elif len(description) < config.MIN_DESCRIPTION_LENGTH:
            recommendations.append(f"描述太短，建议至少{config.MIN_DESCRIPTION_LENGTH}个字符")
        elif len(description) > config.MAX_DESCRIPTION_LENGTH:
            recommendations.append(f"描述太长，建议不超过{config.MAX_DESCRIPTION_LENGTH}个字符")
        return recommendations

    def _check_headings(self):
        """检查标题结构"""
        headings = {}
        h1_tags = self.soup.find_all('h1')

        headings['h1'] = {
            'count': len(h1_tags),
            'content': [h.get_text().strip() for h in h1_tags],
            'recommendations': self._get_heading_recommendations(h1_tags)
        }

        # 检查其他标题标签
        for i in range(2, 7):
            h_tags = self.soup.find_all(f'h{i}')
            headings[f'h{i}'] = {
                'count': len(h_tags),
                'content': [h.get_text().strip() for h in h_tags[:5]]  # 只显示前5个
            }

        return headings

    def _get_heading_recommendations(self, h1_tags):
        """获取标题建议"""
        recommendations = []
        if len(h1_tags) == 0:
            recommendations.append("添加H1标签")
        elif len(h1_tags) > 1:
            recommendations.append("每个页面应该只有一个H1标签")
        return recommendations

    def _check_images(self):
        """检查图片"""
        images = self.soup.find_all('img')
        total_images = len(images)

        # 收集缺少ALT属性的图片详细信息
        images_without_alt = []
        for img in images:
            if not img.get('alt'):
                img_src = img.get('src', '')
                # 处理相对URL
                if img_src.startswith('/'):
                    img_src = f"{self.url.rstrip('/')}{img_src}"
                elif img_src.startswith('./'):
                    img_src = f"{self.url.rstrip('/')}/{img_src[2:]}"
                elif not img_src.startswith(('http://', 'https://')):
                    # 相对路径
                    base_url = '/'.join(self.url.rstrip('/').split('/')[:-1]) if '/' in self.url.rstrip('/') else self.url.rstrip('/')
                    img_src = f"{base_url}/{img_src}"

                images_without_alt.append({
                    'src': img_src,
                    'alt': img.get('alt', ''),
                    'title': img.get('title', ''),
                    'width': img.get('width', ''),
                    'height': img.get('height', ''),
                })

        return {
            'total': total_images,
            'without_alt': len(images_without_alt),
            'without_alt_details': images_without_alt,  # 详细信息
            'recommendations': [f"{len(images_without_alt)}张图片缺少alt属性"] if len(images_without_alt) > 0 else []
        }

    def _check_robots_txt(self):
        """检查robots.txt"""
        try:
            robots_url = urljoin(self.url, '/robots.txt')
            response = requests.get(robots_url, timeout=10)
            exists = response.status_code == 200
            return {
                'exists': exists,
                'url': robots_url,
                'recommendations': [] if exists else ['建议添加robots.txt文件']
            }
        except:
            robots_url = urljoin(self.url, '/robots.txt')
            return {
                'exists': False,
                'url': robots_url,
                'recommendations': ['无法访问robots.txt文件']
            }

    def _check_sitemap(self):
        """检查sitemap"""
        # 常见的sitemap路径
        sitemap_paths = [
            '/sitemap.xml',
            '/sitemap_index.xml',
            '/sitemaps.xml',
            '/sitemap/sitemap.xml',
            '/sitemap/index.xml',
            '/wp-sitemap.xml',  # WordPress
            '/sitemap-index.xml'
        ]

        checked_urls = []

        for sitemap_path in sitemap_paths:
            try:
                full_url = urljoin(self.url, sitemap_path)
                checked_urls.append(full_url)
                response = requests.get(full_url, timeout=10)
                if response.status_code == 200:
                    # 检查内容是否真的是XML sitemap
                    content = response.text.lower()
                    if 'sitemap' in content and ('xml' in content or 'urlset' in content):
                        return {
                            'exists': True,
                            'url': full_url,
                            'checked_urls': checked_urls,
                            'recommendations': []
                        }
            except:
                continue

        # 也检查robots.txt中是否有sitemap声明
        try:
            robots_url = urljoin(self.url, '/robots.txt')
            robots_response = requests.get(robots_url, timeout=10)
            if robots_response.status_code == 200:
                robots_content = robots_response.text.lower()
                if 'sitemap:' in robots_content:
                    # 提取sitemap URL
                    for line in robots_response.text.split('\n'):
                        if line.lower().startswith('sitemap:'):
                            sitemap_url = line.split(':', 1)[1].strip()
                            return {
                                'exists': True,
                                'url': sitemap_url,
                                'checked_urls': checked_urls,
                                'found_in_robots': True,
                                'recommendations': []
                            }
        except:
            pass

        return {
            'exists': False,
            'checked_urls': checked_urls,
            'recommendations': ['建议添加XML网站地图，常见位置: /sitemap.xml']
        }

    def _check_url_structure(self):
        """检查URL结构"""
        parsed_url = urlparse(self.url)
        path = parsed_url.path

        # 检查URL是否友好
        is_friendly = bool(re.match(r'^[a-zA-Z0-9\-/]*$', path))
        has_parameters = bool(parsed_url.query)

        recommendations = []

        if not is_friendly:
            recommendations.append("URL包含特殊字符，建议使用友好的URL结构")

        if has_parameters:
            recommendations.append("URL包含查询参数，可能影响SEO")

        return {
            'friendly': is_friendly,
            'has_parameters': has_parameters,
            'recommendations': recommendations
        }

    def _check_internal_links(self):
        """检查内部链接"""
        links = self.soup.find_all('a', href=True)
        internal_links = []

        for link in links:
            href = link['href']
            if href.startswith('/') or self.domain in href:
                internal_links.append(href)

        return {
            'count': len(internal_links),
            'recommendations': ['增加内部链接以改善网站结构'] if len(internal_links) < 10 else []
        }

    def _check_external_links(self):
        """检查外部链接"""
        links = self.soup.find_all('a', href=True)
        external_links = []

        for link in links:
            href = link['href']
            if href.startswith('http') and self.domain not in href:
                external_links.append(href)

        return {
            'count': len(external_links),
            'recommendations': []
        }



    def _get_content_recommendations(self, word_count):
        """获取内容建议"""
        recommendations = []
        if word_count < config.MIN_CONTENT_LENGTH:
            recommendations.append(f"内容太少，建议至少{config.MIN_CONTENT_LENGTH}个词")
        elif word_count < config.OPTIMAL_CONTENT_LENGTH:
            recommendations.append(f"建议增加内容到{config.OPTIMAL_CONTENT_LENGTH}个词以上")
        return recommendations

    def _check_duplicate_content(self):
        """检查重复内容（简单实现）"""
        # 这里只是一个简单的实现，实际应用中可能需要更复杂的算法
        return {
            'detected': False,
            'recommendations': []
        }



    def _get_performance_recommendations(self, load_time):
        """获取性能建议"""
        recommendations = []
        if load_time > 6:
            recommendations.append("页面加载时间过长，建议优化")
        elif load_time > 4:
            recommendations.append("页面加载时间较长，可以进一步优化")
        elif load_time > 2:
            recommendations.append("页面加载时间良好，可以微调优化")
        return recommendations



    def _get_size_recommendations(self, page_size):
        """获取大小建议"""
        recommendations = []
        if page_size > 2000:
            recommendations.append("页面过大，建议压缩图片和资源")
        elif page_size > 1000:
            recommendations.append("页面较大，建议优化资源")
        return recommendations

    def _check_responsive_design(self):
        """检查响应式设计"""
        # 简单检查CSS中是否有媒体查询
        style_tags = self.soup.find_all('style')
        link_tags = self.soup.find_all('link', rel='stylesheet')

        has_media_query = False

        # 检查内联样式
        for style in style_tags:
            if '@media' in style.get_text():
                has_media_query = True
                break

        return {
            'detected': has_media_query,
            'recommendations': [] if has_media_query else ['建议添加响应式设计支持']
        }

    def _check_og_tags(self):
        """检查Open Graph标签"""
        og_data = {}
        recommendations = []

        # 检查主要的OG标签
        og_tags = {
            'og:title': 'title',
            'og:description': 'description',
            'og:image': 'image',
            'og:url': 'url',
            'og:type': 'type',
            'og:site_name': 'site_name'
        }

        for og_property, key in og_tags.items():
            meta_tag = self.soup.find('meta', property=og_property)
            if meta_tag:
                content = meta_tag.get('content', '').strip()
                og_data[key] = content

                # 检查内容是否为空
                if not content:
                    recommendations.append(f"{og_property} 标签存在但内容为空")
            else:
                og_data[key] = ''
                recommendations.append(f"缺少 {og_property} 标签")

        # 特殊检查：图片URL处理
        if og_data.get('image'):
            image_url = og_data['image']
            # 处理相对URL
            if image_url.startswith('/'):
                image_url = f"{self.url.rstrip('/')}{image_url}"
            elif image_url.startswith('./'):
                image_url = f"{self.url.rstrip('/')}/{image_url[2:]}"
            elif not image_url.startswith(('http://', 'https://')):
                # 相对路径
                base_url = '/'.join(self.url.rstrip('/').split('/')[:-1]) if '/' in self.url.rstrip('/') else self.url.rstrip('/')
                image_url = f"{base_url}/{image_url}"
            og_data['image_full_url'] = image_url
        else:
            og_data['image_full_url'] = ''

        # 检查图片尺寸建议
        if og_data.get('image'):
            # 检查是否有图片尺寸标签
            width_tag = self.soup.find('meta', property='og:image:width')
            height_tag = self.soup.find('meta', property='og:image:height')

            if not width_tag or not height_tag:
                recommendations.append("建议添加 og:image:width 和 og:image:height 标签")
            else:
                og_data['image_width'] = width_tag.get('content', '') if width_tag else ''
                og_data['image_height'] = height_tag.get('content', '') if height_tag else ''

        return {
            'tags': og_data,
            'recommendations': recommendations,
            'has_basic_tags': bool(og_data.get('title') and og_data.get('description') and og_data.get('image'))
        }

    def _check_twitter_cards(self):
        """检查Twitter Cards标签"""
        twitter_data = {}
        recommendations = []

        # 检查主要的Twitter Cards标签
        twitter_tags = {
            'twitter:card': 'card',
            'twitter:title': 'title',
            'twitter:description': 'description',
            'twitter:image': 'image',
            'twitter:site': 'site',
            'twitter:creator': 'creator'
        }

        for twitter_name, key in twitter_tags.items():
            meta_tag = self.soup.find('meta', attrs={'name': twitter_name})
            if meta_tag:
                content = meta_tag.get('content', '').strip()
                twitter_data[key] = content

                # 检查内容是否为空
                if not content:
                    recommendations.append(f"{twitter_name} 标签存在但内容为空")
            else:
                twitter_data[key] = ''
                if twitter_name in ['twitter:card', 'twitter:title', 'twitter:description']:
                    recommendations.append(f"缺少 {twitter_name} 标签")

        # 特殊检查：图片URL处理
        if twitter_data.get('image'):
            image_url = twitter_data['image']
            # 处理相对URL
            if image_url.startswith('/'):
                image_url = f"{self.url.rstrip('/')}{image_url}"
            elif image_url.startswith('./'):
                image_url = f"{self.url.rstrip('/')}/{image_url[2:]}"
            elif not image_url.startswith(('http://', 'https://')):
                base_url = '/'.join(self.url.rstrip('/').split('/')[:-1]) if '/' in self.url.rstrip('/') else self.url.rstrip('/')
                image_url = f"{base_url}/{image_url}"
            twitter_data['image_full_url'] = image_url
        else:
            twitter_data['image_full_url'] = ''

        return {
            'tags': twitter_data,
            'recommendations': recommendations,
            'has_basic_tags': bool(twitter_data.get('card') and twitter_data.get('title') and twitter_data.get('description'))
        }

    def _check_icons(self):
        """检查favicon和apple touch icon"""
        icons_data = {}
        recommendations = []

        # 检查favicon
        favicon_found = False
        favicon_links = self.soup.find_all('link', rel=lambda x: x and 'icon' in x.lower())

        if favicon_links:
            favicon_found = True
            favicon_urls = []
            for link in favicon_links:
                href = link.get('href', '')
                if href:
                    # 转换为绝对URL
                    if href.startswith('/'):
                        full_url = f"{self.url.rstrip('/')}{href}"
                    elif href.startswith('./'):
                        full_url = f"{self.url.rstrip('/')}/{href[2:]}"
                    elif not href.startswith(('http://', 'https://')):
                        base_url = '/'.join(self.url.rstrip('/').split('/')[:-1]) if '/' in self.url.rstrip('/') else self.url.rstrip('/')
                        full_url = f"{base_url}/{href}"
                    else:
                        full_url = href
                    favicon_urls.append(full_url)
            icons_data['favicon'] = {
                'found': True,
                'urls': favicon_urls
            }
        else:
            # 检查默认位置的favicon.ico
            try:
                favicon_url = urljoin(self.url, '/favicon.ico')
                response = requests.head(favicon_url, timeout=10)
                if response.status_code == 200:
                    favicon_found = True
                    icons_data['favicon'] = {
                        'found': True,
                        'urls': ['/favicon.ico']
                    }
            except:
                pass

        if not favicon_found:
            icons_data['favicon'] = {'found': False, 'urls': []}
            recommendations.append("缺少favicon图标")

        # 检查Apple Touch Icon
        apple_icon_links = self.soup.find_all('link', rel=lambda x: x and 'apple-touch-icon' in x.lower())

        if apple_icon_links:
            apple_urls = []
            apple_sizes = []
            for link in apple_icon_links:
                href = link.get('href', '')
                if href:
                    # 转换为绝对URL
                    if href.startswith('/'):
                        full_url = f"{self.url.rstrip('/')}{href}"
                    elif href.startswith('./'):
                        full_url = f"{self.url.rstrip('/')}/{href[2:]}"
                    elif not href.startswith(('http://', 'https://')):
                        base_url = '/'.join(self.url.rstrip('/').split('/')[:-1]) if '/' in self.url.rstrip('/') else self.url.rstrip('/')
                        full_url = f"{base_url}/{href}"
                    else:
                        full_url = href
                    apple_urls.append(full_url)
                    apple_sizes.append(link.get('sizes', ''))
            icons_data['apple_touch_icon'] = {
                'found': True,
                'urls': apple_urls,
                'sizes': apple_sizes
            }
        else:
            icons_data['apple_touch_icon'] = {'found': False, 'urls': [], 'sizes': []}
            recommendations.append("缺少Apple Touch Icon")

        return {
            'icons': icons_data,
            'recommendations': recommendations,
            'has_all_icons': favicon_found and bool(apple_icon_links)
        }

    def _check_canonical(self):
        """检查canonical URL"""
        canonical_tag = self.soup.find('link', rel='canonical')

        if canonical_tag:
            canonical_url = canonical_tag.get('href', '').strip()
            recommendations = []

            # 检查canonical URL是否为空
            if not canonical_url:
                recommendations.append("canonical标签存在但href为空")
            # 检查是否为绝对URL
            elif not canonical_url.startswith(('http://', 'https://')):
                recommendations.append("canonical URL应该使用绝对URL")

            return {
                'found': True,
                'url': canonical_url,
                'recommendations': recommendations
            }
        else:
            return {
                'found': False,
                'url': '',
                'recommendations': ['缺少canonical URL标签']
            }

    def _check_cache_policy(self):
        """检查缓存策略"""
        cache_data = {}
        recommendations = []

        try:
            # 获取HTTP响应头
            response = requests.head(self.url, timeout=10)
            headers = response.headers

            # 检查Cache-Control
            cache_control = headers.get('Cache-Control', '')
            cache_data['cache_control'] = cache_control

            # 检查ETag
            etag = headers.get('ETag', '')
            cache_data['etag'] = etag

            # 检查Last-Modified
            last_modified = headers.get('Last-Modified', '')
            cache_data['last_modified'] = last_modified

            # 检查Expires
            expires = headers.get('Expires', '')
            cache_data['expires'] = expires

            # 分析缓存策略
            if not cache_control:
                recommendations.append("缺少Cache-Control头")
            elif 'no-cache' in cache_control.lower() and 'no-store' in cache_control.lower():
                recommendations.append("缓存策略过于严格，可能影响性能")

            if not etag and not last_modified:
                recommendations.append("缺少ETag或Last-Modified头，无法进行条件请求")

            cache_data['has_cache_headers'] = bool(cache_control or etag or last_modified or expires)

        except Exception as e:
            cache_data = {
                'cache_control': '',
                'etag': '',
                'last_modified': '',
                'expires': '',
                'has_cache_headers': False
            }
            recommendations.append(f"无法检查缓存策略: {e}")

        return {
            'headers': cache_data,
            'recommendations': recommendations
        }

    def _check_pwa(self):
        """检查PWA相关配置"""
        pwa_data = {}
        recommendations = []

        # 检查manifest.json
        manifest_link = self.soup.find('link', rel='manifest')
        if manifest_link:
            manifest_url = manifest_link.get('href', '')
            pwa_data['manifest'] = {
                'found': True,
                'url': manifest_url
            }

            # 尝试获取manifest内容
            try:
                if manifest_url.startswith('/'):
                    manifest_url = f"{self.url.rstrip('/')}{manifest_url}"
                elif not manifest_url.startswith(('http://', 'https://')):
                    base_url = '/'.join(self.url.rstrip('/').split('/')[:-1]) if '/' in self.url.rstrip('/') else self.url.rstrip('/')
                    manifest_url = f"{base_url}/{manifest_url}"

                manifest_response = requests.get(manifest_url, timeout=10)
                if manifest_response.status_code == 200:
                    import json
                    manifest_content = manifest_response.json()
                    pwa_data['manifest']['content'] = manifest_content

                    # 检查关键字段
                    required_fields = ['name', 'short_name', 'start_url', 'display', 'icons']
                    missing_fields = [field for field in required_fields if field not in manifest_content]
                    if missing_fields:
                        recommendations.append(f"manifest.json缺少字段: {', '.join(missing_fields)}")

            except Exception as e:
                recommendations.append(f"无法读取manifest.json: {e}")
        else:
            pwa_data['manifest'] = {'found': False, 'url': ''}
            recommendations.append("缺少manifest.json")

        # 检查Service Worker (通过常见的注册脚本)
        scripts = self.soup.find_all('script')
        service_worker_found = False

        for script in scripts:
            script_content = script.get_text()
            if 'serviceWorker' in script_content and 'register' in script_content:
                service_worker_found = True
                break

        pwa_data['service_worker'] = {
            'found': service_worker_found
        }

        if not service_worker_found:
            recommendations.append("未检测到Service Worker注册")

        # 检查viewport meta标签 (PWA必需)
        viewport = self.soup.find('meta', attrs={'name': 'viewport'})
        pwa_data['viewport'] = {
            'found': bool(viewport),
            'content': viewport.get('content', '') if viewport else ''
        }

        if not viewport:
            recommendations.append("PWA需要viewport meta标签")

        # 检查theme-color
        theme_color = self.soup.find('meta', attrs={'name': 'theme-color'})
        pwa_data['theme_color'] = {
            'found': bool(theme_color),
            'color': theme_color.get('content', '') if theme_color else ''
        }

        if not theme_color:
            recommendations.append("建议添加theme-color meta标签")

        return {
            'features': pwa_data,
            'recommendations': recommendations,
            'pwa_ready': bool(manifest_link and service_worker_found and viewport)
        }


