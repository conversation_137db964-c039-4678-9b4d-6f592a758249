import os
import re
from datetime import datetime
from jinja2 import Template
import config

class ReportGenerator:
    def __init__(self):
        self.template_dir = 'templates'
        self.static_dir = 'static'
        self.reports_dir = 'reports'

    def generate_html_report(self, seo_results, url, output_file='seo_report.html'):
        """生成HTML报告"""

        # 创建报告目录结构
        reports_dir = self.reports_dir
        reports_static_dir = os.path.join(reports_dir, 'static')

        # 确保报告目录存在
        os.makedirs(reports_dir, exist_ok=True)
        os.makedirs(reports_static_dir, exist_ok=True)

        # 创建CSS样式文件（在reports/static目录下）
        self._create_css_file(reports_static_dir)

        # 如果output_file没有包含路径，则放到reports目录下
        if not os.path.dirname(output_file):
            output_file = os.path.join(reports_dir, output_file)

        # 创建HTML模板
        template_content = self._get_html_template()
        template = Template(template_content)

        # 准备模板数据
        template_data = {
            'url': url,
            'results': seo_results,
            'report_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'config': config.REPORT_CONFIG
        }

        # 渲染HTML
        html_content = template.render(**template_data)

        # 保存文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"报告已生成: {output_file}")
        return output_file

    def generate_filename_from_title(self, seo_results, url, prefix="seo_report"):
        """根据网站标题和时间生成文件名"""
        # 获取网站标题
        title = ""
        if seo_results and 'basic_seo' in seo_results and 'title' in seo_results['basic_seo']:
            title = seo_results['basic_seo']['title'].get('content', '')

        # 如果没有标题，使用域名
        if not title:
            # 从URL提取域名
            domain = url.replace('https://', '').replace('http://', '').split('/')[0]
            title = domain

        # 提取分隔符前的主要名称部分
        # 常见的分隔符：- | · — • : ｜ 等
        separators = [' - ', ' | ', ' · ', ' — ', ' • ', ' : ', ' ｜ ', '|', '-']

        for separator in separators:
            if separator in title:
                title = title.split(separator)[0].strip()
                break

        # 清理标题，移除不适合文件名的字符
        title = re.sub(r'[^\w\s]', '', title)   # 移除特殊字符，保留字母数字和空格
        title = re.sub(r'\s+', '_', title)      # 将空格替换为下划线
        title = title.strip('_')                # 移除首尾的下划线

        # 限制标题长度
        if len(title) > 30:
            title = title[:30]

        # 如果标题为空，使用默认名称
        if not title:
            title = "website"

        # 生成时间戳
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 生成文件名
        filename = f"{prefix}_{title}_{timestamp}.html"

        return filename

    def _create_css_file(self, target_dir=None):
        """创建CSS样式文件"""
        if target_dir is None:
            target_dir = self.static_dir
        css_content = """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header .url {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .score-overview {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .score-circle {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5em;
            font-weight: bold;
            color: white;
        }

        .score-a { background: #4CAF50; }
        .score-b { background: #8BC34A; }
        .score-c { background: #FFC107; }
        .score-d { background: #FF9800; }
        .score-f { background: #F44336; }

        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .section-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }

        .section-header h2 {
            color: #495057;
            font-size: 1.5em;
        }

        .section-content {
            padding: 20px;
        }

        .check-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .check-item:last-child {
            border-bottom: none;
        }

        .check-name {
            font-weight: 500;
            flex: 1;
        }

        .check-score {
            padding: 5px 15px;
            border-radius: 20px;
            color: white;
            font-weight: bold;
            margin-left: 10px;
        }

        .score-excellent { background: #4CAF50; }
        .score-good { background: #8BC34A; }
        .score-average { background: #FFC107; }
        .score-poor { background: #FF9800; }
        .score-bad { background: #F44336; }

        .recommendations {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
        }

        .recommendations h4 {
            color: #856404;
            margin-bottom: 10px;
        }

        .recommendations ul {
            list-style-type: none;
            padding-left: 0;
        }

        .recommendations li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }

        .recommendations li:before {
            content: "⚠";
            position: absolute;
            left: 0;
            color: #856404;
        }

        .details {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 0.9em;
        }

        .footer {
            text-align: center;
            padding: 30px;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
            margin-top: 30px;
        }

        /* 缺少ALT属性的图片样式 */
        .missing-alt-images {
            background: #fff8e1;
            border: 1px solid #ffcc02;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
        }

        .missing-alt-images h4 {
            color: #e65100;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .missing-alt-item {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 15px;
            overflow: hidden;
        }

        .missing-alt-item:last-child {
            margin-bottom: 0;
        }

        .alt-status {
            background: #f5f5f5;
            padding: 10px 15px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alt-label {
            font-weight: bold;
            color: #666;
            min-width: 30px;
        }

        .alt-missing {
            color: #ff9800;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .image-info {
            padding: 15px;
        }

        .image-url {
            margin-bottom: 15px;
            word-break: break-all;
        }

        .image-link {
            color: #1976d2;
            text-decoration: none;
            font-family: monospace;
            font-size: 0.9em;
        }

        .image-link:hover {
            text-decoration: underline;
        }

        .image-preview {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100px;
            background: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 10px;
        }

        .preview-img {
            max-width: 200px;
            max-height: 150px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .preview-error {
            color: #666;
            font-style: italic;
            text-align: center;
        }

        /* OG标签预览样式 */
        .og-preview {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
        }

        .og-preview h4 {
            color: #2e7d32;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .og-image-container {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }

        .og-preview-img {
            width: 100%;
            max-width: 400px;
            height: auto;
            display: block;
            margin: 0 auto;
        }

        .og-image-error {
            padding: 40px;
            text-align: center;
            color: #666;
            font-style: italic;
            background: #f9f9f9;
        }

        .og-image-info {
            padding: 15px;
            background: #f8f9fa;
            border-top: 1px solid #e0e0e0;
            word-break: break-all;
        }

        /* Twitter Cards预览样式 */
        .twitter-preview {
            background: #e3f2fd;
            border: 1px solid #1976d2;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
        }

        .twitter-preview h4 {
            color: #1565c0;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .twitter-image-container {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }

        .twitter-preview-img {
            width: 100%;
            max-width: 400px;
            height: auto;
            display: block;
            margin: 0 auto;
        }

        .twitter-image-error {
            padding: 40px;
            text-align: center;
            color: #666;
            font-style: italic;
            background: #f9f9f9;
        }

        .twitter-image-info {
            padding: 15px;
            background: #f8f9fa;
            border-top: 1px solid #e0e0e0;
            word-break: break-all;
        }

        /* 文件链接样式 */
        .file-link {
            color: #1976d2;
            text-decoration: none;
            font-weight: 500;
            border-bottom: 1px dotted #1976d2;
        }

        .file-link:hover {
            color: #0d47a1;
            text-decoration: none;
            border-bottom: 1px solid #0d47a1;
        }

        .link-status {
            margin-left: 10px;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.85em;
            font-weight: 500;
        }

        .status-good {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .status-warning {
            background: #fff3e0;
            color: #f57c00;
        }

        .checked-urls {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #ff9800;
        }

        .url-list {
            list-style: none;
            padding: 0;
            margin: 10px 0 0 0;
        }

        .url-list li {
            margin: 8px 0;
            padding: 8px;
            background: white;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
        }

        .checked-url {
            font-family: monospace;
            font-size: 0.9em;
        }

        .icon-link {
            font-size: 0.9em;
            margin: 2px 4px;
            display: inline-block;
        }

        /* 图标预览样式 */
        .icon-section {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }

        .icon-preview {
            margin-top: 10px;
        }

        .icon-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
        }

        .icon-display {
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 48px;
            height: 48px;
            background: #f5f5f5;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .favicon-img {
            max-width: 32px;
            max-height: 32px;
            width: auto;
            height: auto;
        }

        .apple-icon-img {
            max-width: 44px;
            max-height: 44px;
            width: auto;
            height: auto;
            border-radius: 8px;
        }

        .icon-error {
            font-size: 0.8em;
            color: #666;
        }

        .icon-url {
            flex: 1;
            word-break: break-all;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2em;
            }

            .score-circle {
                width: 120px;
                height: 120px;
                font-size: 2em;
            }

            .check-item {
                flex-direction: column;
                align-items: flex-start;
            }

            .check-score {
                margin-left: 0;
                margin-top: 10px;
            }
        }
        """

        css_file = os.path.join(target_dir, 'style.css')
        with open(css_file, 'w', encoding='utf-8') as f:
            f.write(css_content)

    def _get_html_template(self):
        """获取HTML模板"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ config.title }} - {{ url }}</title>
    <link rel="stylesheet" href="static/style.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>{{ config.title }}</h1>
            <div class="url">{{ url }}</div>
            <div style="margin-top: 10px; opacity: 0.8;">
                生成时间: {{ report_date }}
            </div>
        </div>



        <!-- 基础SEO -->
        {% if results.basic_seo %}
        <div class="section">
            <div class="section-header">
                <h2>🔍 基础SEO检查</h2>
            </div>
            <div class="section-content">
                <!-- 标题检查 -->
                {% if results.basic_seo.title %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面标题</strong>
                        <div class="details">
                            内容: {{ results.basic_seo.title.content or '无' }}<br>
                            长度: {{ results.basic_seo.title.length }} 字符
                        </div>
                        {% if results.basic_seo.title.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.basic_seo.title.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- Meta描述检查 -->
                {% if results.basic_seo.meta_description %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>Meta描述</strong>
                        <div class="details">
                            内容: {{ results.basic_seo.meta_description.content or '无' }}<br>
                            长度: {{ results.basic_seo.meta_description.length }} 字符
                        </div>
                        {% if results.basic_seo.meta_description.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.basic_seo.meta_description.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- 标题结构检查 -->
                {% if results.basic_seo.headings %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>标题结构 (H1-H6)</strong>
                        <div class="details">
                            {% for heading, data in results.basic_seo.headings.items() %}
                            {{ heading.upper() }}: {{ data.count }} 个
                            {% if data.content %}
                            <br>内容: {{ data.content[:3]|join(', ') }}{% if data.content|length > 3 %}...{% endif %}
                            {% endif %}
                            <br>
                            {% endfor %}
                        </div>
                        {% if results.basic_seo.headings.h1.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.basic_seo.headings.h1.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- 图片检查 -->
                {% if results.basic_seo.images %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>图片优化</strong>
                        <div class="details">
                            总图片数: {{ results.basic_seo.images.total }}<br>
                            缺少alt属性: {{ results.basic_seo.images.without_alt }}
                        </div>
                        {% if results.basic_seo.images.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.basic_seo.images.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}

                        <!-- 显示缺少ALT属性的图片详情 -->
                        {% if results.basic_seo.images.without_alt_details %}
                        <div class="missing-alt-images">
                            <h4>缺少ALT属性的图片:</h4>
                            {% for img in results.basic_seo.images.without_alt_details %}
                            <div class="missing-alt-item">
                                <div class="alt-status">
                                    <span class="alt-label">Alt</span>
                                    <span class="alt-missing">⚠ Missing</span>
                                </div>
                                <div class="image-info">
                                    <div class="image-url">
                                        <strong>URL:</strong>
                                        <a href="{{ img.src }}" target="_blank" class="image-link">{{ img.src }}</a>
                                    </div>
                                    <div class="image-preview">
                                        <img src="{{ img.src }}" alt="预览图" class="preview-img"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="preview-error" style="display:none;">
                                            <span>🖼️ 图片无法加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- OG标签检查 -->
                {% if results.basic_seo.og_tags %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>Open Graph 标签</strong>
                        <div class="details">
                            完整性: {{ '完整' if results.basic_seo.og_tags.has_basic_tags else '不完整' }}<br>
                            {% if results.basic_seo.og_tags.tags.title %}
                            标题: {{ results.basic_seo.og_tags.tags.title }}<br>
                            {% endif %}
                            {% if results.basic_seo.og_tags.tags.description %}
                            描述: {{ results.basic_seo.og_tags.tags.description }}<br>
                            {% endif %}
                            {% if results.basic_seo.og_tags.tags.type %}
                            类型: {{ results.basic_seo.og_tags.tags.type }}<br>
                            {% endif %}
                            {% if results.basic_seo.og_tags.tags.url %}
                            URL: <a href="{{ results.basic_seo.og_tags.tags.url }}" target="_blank" class="file-link">
                                {{ results.basic_seo.og_tags.tags.url }}
                            </a><br>
                            {% endif %}
                            {% if results.basic_seo.og_tags.tags.site_name %}
                            网站名: {{ results.basic_seo.og_tags.tags.site_name }}<br>
                            {% endif %}
                        </div>

                        <!-- OG图片预览 -->
                        {% if results.basic_seo.og_tags.tags.image %}
                        <div class="og-preview">
                            <h4>OG图片预览:</h4>
                            <div class="og-image-container">
                                <img src="{{ results.basic_seo.og_tags.tags.image_full_url }}" alt="OG图片预览" class="og-preview-img"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                <div class="og-image-error" style="display:none;">
                                    <span>🖼️ 图片无法加载</span>
                                </div>
                                <div class="og-image-info">
                                    <strong>图片URL:</strong>
                                    <a href="{{ results.basic_seo.og_tags.tags.image_full_url }}" target="_blank" class="image-link">
                                        {{ results.basic_seo.og_tags.tags.image_full_url }}
                                    </a>
                                    {% if results.basic_seo.og_tags.tags.image_width and results.basic_seo.og_tags.tags.image_height %}
                                    <br><strong>尺寸:</strong> {{ results.basic_seo.og_tags.tags.image_width }} x {{ results.basic_seo.og_tags.tags.image_height }}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        {% if results.basic_seo.og_tags.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.basic_seo.og_tags.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- Twitter Cards检查 -->
                {% if results.basic_seo.twitter_cards %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>Twitter Cards</strong>
                        <div class="details">
                            完整性: {{ '完整' if results.basic_seo.twitter_cards.has_basic_tags else '不完整' }}<br>
                            {% if results.basic_seo.twitter_cards.tags.card %}
                            卡片类型: {{ results.basic_seo.twitter_cards.tags.card }}<br>
                            {% endif %}
                            {% if results.basic_seo.twitter_cards.tags.title %}
                            标题: {{ results.basic_seo.twitter_cards.tags.title }}<br>
                            {% endif %}
                            {% if results.basic_seo.twitter_cards.tags.description %}
                            描述: {{ results.basic_seo.twitter_cards.tags.description }}<br>
                            {% endif %}
                            {% if results.basic_seo.twitter_cards.tags.site %}
                            网站: {{ results.basic_seo.twitter_cards.tags.site }}<br>
                            {% endif %}
                        </div>

                        <!-- Twitter图片预览 -->
                        {% if results.basic_seo.twitter_cards.tags.image %}
                        <div class="twitter-preview">
                            <h4>Twitter图片预览:</h4>
                            <div class="twitter-image-container">
                                <img src="{{ results.basic_seo.twitter_cards.tags.image_full_url }}" alt="Twitter图片预览" class="twitter-preview-img"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                <div class="twitter-image-error" style="display:none;">
                                    <span>🖼️ 图片无法加载</span>
                                </div>
                                <div class="twitter-image-info">
                                    <strong>图片URL:</strong>
                                    <a href="{{ results.basic_seo.twitter_cards.tags.image_full_url }}" target="_blank" class="image-link">
                                        {{ results.basic_seo.twitter_cards.tags.image_full_url }}
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        {% if results.basic_seo.twitter_cards.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.basic_seo.twitter_cards.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- 网站图标检查 -->
                {% if results.basic_seo.icons %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>网站图标</strong>
                        <div class="details">
                            <!-- Favicon 预览 -->
                            <div class="icon-section">
                                <strong>Favicon:</strong> {{ '存在' if results.basic_seo.icons.icons.favicon.found else '缺失' }}
                                {% if results.basic_seo.icons.icons.favicon.found %}
                                <div class="icon-preview">
                                    {% for url in results.basic_seo.icons.icons.favicon.urls %}
                                    <div class="icon-item">
                                        <div class="icon-display">
                                            <img src="{{ url }}"
                                                 alt="Favicon" class="favicon-img"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                            <span class="icon-error" style="display:none;">🖼️ 无法加载</span>
                                        </div>
                                        <div class="icon-url">
                                            <a href="{{ url }}" target="_blank" class="file-link icon-link">{{ url }}</a>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <!-- Apple Touch Icon 预览 -->
                            <div class="icon-section">
                                <strong>Apple Touch Icon:</strong> {{ '存在' if results.basic_seo.icons.icons.apple_touch_icon.found else '缺失' }}
                                {% if results.basic_seo.icons.icons.apple_touch_icon.found %}
                                <div class="icon-preview">
                                    {% for url in results.basic_seo.icons.icons.apple_touch_icon.urls %}
                                    <div class="icon-item">
                                        <div class="icon-display">
                                            <img src="{{ url }}"
                                                 alt="Apple Touch Icon" class="apple-icon-img"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                            <span class="icon-error" style="display:none;">🖼️ 无法加载</span>
                                        </div>
                                        <div class="icon-url">
                                            <a href="{{ url }}" target="_blank" class="file-link icon-link">{{ url }}</a>
                                            {% if results.basic_seo.icons.icons.apple_touch_icon.sizes[loop.index0] %}
                                            <br><small>尺寸: {{ results.basic_seo.icons.icons.apple_touch_icon.sizes[loop.index0] }}</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% if results.basic_seo.icons.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.basic_seo.icons.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- Canonical URL检查 -->
                {% if results.basic_seo.canonical %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>Canonical URL</strong>
                        <div class="details">
                            存在: {{ '是' if results.basic_seo.canonical.found else '否' }}<br>
                            {% if results.basic_seo.canonical.found %}
                            URL: <a href="{{ results.basic_seo.canonical.url }}" target="_blank" class="file-link">
                                {{ results.basic_seo.canonical.url }}
                            </a>
                            {% endif %}
                        </div>
                        {% if results.basic_seo.canonical.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.basic_seo.canonical.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 技术SEO -->
        {% if results.technical_seo %}
        <div class="section">
            <div class="section-header">
                <h2>⚙️ 技术SEO检查</h2>
            </div>
            <div class="section-content">
                <!-- Robots.txt检查 -->
                {% if results.technical_seo.robots_txt %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>Robots.txt</strong>
                        <div class="details">
                            存在: {{ '是' if results.technical_seo.robots_txt.exists else '否' }}<br>
                            {% if results.technical_seo.robots_txt.url %}
                            URL: <a href="{{ results.technical_seo.robots_txt.url }}" target="_blank" class="file-link">
                                {{ results.technical_seo.robots_txt.url }}
                            </a>
                            {% if results.technical_seo.robots_txt.exists %}
                            <span class="link-status status-good">✅ 可访问</span>
                            {% else %}
                            <span class="link-status status-warning">⚠️ 不可访问</span>
                            {% endif %}
                            {% endif %}
                        </div>
                        {% if results.technical_seo.robots_txt.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.technical_seo.robots_txt.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- Sitemap检查 -->
                {% if results.technical_seo.sitemap %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>XML网站地图</strong>
                        <div class="details">
                            存在: {{ '是' if results.technical_seo.sitemap.exists else '否' }}<br>
                            {% if results.technical_seo.sitemap.exists and results.technical_seo.sitemap.url %}
                            URL: <a href="{{ results.technical_seo.sitemap.url }}" target="_blank" class="file-link">
                                {{ results.technical_seo.sitemap.url }}
                            </a>
                            <span class="link-status status-good">✅ 可访问</span>
                            {% if results.technical_seo.sitemap.found_in_robots %}
                            <br><small>📍 在robots.txt中发现</small>
                            {% endif %}
                            {% endif %}

                            {% if not results.technical_seo.sitemap.exists %}
                            <div class="checked-urls">
                                <strong>已检查的位置:</strong>
                                <ul class="url-list">
                                    {% for url in results.technical_seo.sitemap.checked_urls %}
                                    <li>
                                        <a href="{{ url }}" target="_blank" class="file-link checked-url">{{ url }}</a>
                                        <span class="link-status status-warning">⚠️ 未找到</span>
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                            {% endif %}
                        </div>
                        {% if results.technical_seo.sitemap.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.technical_seo.sitemap.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- 内部链接检查 -->
                {% if results.technical_seo.internal_links %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>内部链接</strong>
                        <div class="details">
                            数量: {{ results.technical_seo.internal_links.count }}
                        </div>
                        {% if results.technical_seo.internal_links.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.technical_seo.internal_links.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- 缓存策略检查 -->
                {% if results.technical_seo.cache_policy %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>缓存策略</strong>
                        <div class="details">
                            配置状态: {{ '已配置' if results.technical_seo.cache_policy.headers.has_cache_headers else '未配置' }}<br>
                            {% if results.technical_seo.cache_policy.headers.cache_control %}
                            Cache-Control: {{ results.technical_seo.cache_policy.headers.cache_control }}<br>
                            {% endif %}
                            {% if results.technical_seo.cache_policy.headers.etag %}
                            ETag: {{ results.technical_seo.cache_policy.headers.etag }}<br>
                            {% endif %}
                            {% if results.technical_seo.cache_policy.headers.last_modified %}
                            Last-Modified: {{ results.technical_seo.cache_policy.headers.last_modified }}<br>
                            {% endif %}
                            {% if results.technical_seo.cache_policy.headers.expires %}
                            Expires: {{ results.technical_seo.cache_policy.headers.expires }}
                            {% endif %}
                        </div>
                        {% if results.technical_seo.cache_policy.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.technical_seo.cache_policy.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- PWA检查 -->
                {% if results.technical_seo.pwa %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>PWA支持</strong>
                        <div class="details">
                            就绪状态: {{ '就绪' if results.technical_seo.pwa.pwa_ready else '未就绪' }}<br>
                            Manifest: {{ '存在' if results.technical_seo.pwa.features.manifest.found else '缺失' }}<br>
                            {% if results.technical_seo.pwa.features.manifest.found %}
                            Manifest URL: <a href="{{ results.technical_seo.pwa.features.manifest.url }}" target="_blank" class="file-link">
                                {{ results.technical_seo.pwa.features.manifest.url }}
                            </a><br>
                            {% endif %}
                            Service Worker: {{ '检测到' if results.technical_seo.pwa.features.service_worker.found else '未检测到' }}<br>
                            Viewport: {{ '存在' if results.technical_seo.pwa.features.viewport.found else '缺失' }}<br>
                            Theme Color: {{ '存在' if results.technical_seo.pwa.features.theme_color.found else '缺失' }}
                            {% if results.technical_seo.pwa.features.theme_color.found %}
                            ({{ results.technical_seo.pwa.features.theme_color.color }})
                            {% endif %}
                        </div>
                        {% if results.technical_seo.pwa.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.technical_seo.pwa.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 性能检查 -->
        {% if results.performance %}
        <div class="section">
            <div class="section-header">
                <h2>🚀 性能检查</h2>
            </div>
            <div class="section-content">
                <!-- 加载时间检查 -->
                {% if results.performance.load_time %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面加载时间</strong>
                        <div class="details">
                            时间: {{ results.performance.load_time.time }} 秒
                        </div>
                        {% if results.performance.load_time.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.performance.load_time.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- 页面大小检查 -->
                {% if results.performance.page_size %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面大小</strong>
                        <div class="details">
                            大小: {{ results.performance.page_size.size }} KB
                        </div>
                        {% if results.performance.page_size.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.performance.page_size.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 移动端友好性 -->
        {% if results.mobile %}
        <div class="section">
            <div class="section-header">
                <h2>📱 移动端友好性</h2>
            </div>
            <div class="section-content">
                <!-- Viewport检查 -->
                {% if results.mobile.viewport %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>Viewport设置</strong>
                        <div class="details">
                            存在: {{ '是' if results.mobile.viewport.exists else '否' }}
                            {% if results.mobile.viewport.content %}
                            <br>内容: {{ results.mobile.viewport.content }}
                            {% endif %}
                        </div>
                        {% if results.mobile.viewport.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.mobile.viewport.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- 响应式设计检查 -->
                {% if results.mobile.responsive %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>响应式设计</strong>
                        <div class="details">
                            检测到: {{ '是' if results.mobile.responsive.detected else '否' }}
                        </div>
                        {% if results.mobile.responsive.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.mobile.responsive.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 安全检查 -->
        {% if results.security %}
        <div class="section">
            <div class="section-header">
                <h2>🔒 安全检查</h2>
            </div>
            <div class="section-content">
                <!-- HTTPS检查 -->
                {% if results.security.https %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>HTTPS加密</strong>
                        <div class="details">
                            启用: {{ '是' if results.security.https.enabled else '否' }}
                        </div>
                        {% if results.security.https.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.security.https.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 内容分析 -->
        {% if results.content_analysis %}
        <div class="section">
            <div class="section-header">
                <h2>📝 内容分析</h2>
            </div>
            <div class="section-content">
                <!-- 内容长度检查 -->
                {% if results.content_analysis.word_count %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>内容长度</strong>
                        <div class="details">
                            词数: {{ results.content_analysis.word_count.count }}
                        </div>
                        {% if results.content_analysis.word_count.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.content_analysis.word_count.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 页脚 -->
        <div class="footer">
            <p>报告由 {{ config.company }} SEO审计工具生成</p>
            <p>生成时间: {{ report_date }}</p>
        </div>
    </div>
</body>
</html>
        """
